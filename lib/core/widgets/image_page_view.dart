import 'package:flutter/material.dart';
import 'package:rozana/widgets/custom_image.dart';

/// A reusable widget for displaying images in a PageView with customizable options
class ImagePageView extends StatefulWidget {
  final List<String> images;
  final int initialIndex;
  final PageController? controller;
  final Function(int)? onPageChanged;
  final Function(int)? onImageTap;
  final BoxFit fit;
  final bool enableInteractiveViewer;
  final double minScale;
  final double maxScale;
  final BorderRadius? borderRadius;
  final String? placeholderAsset;
  final Widget? loadingWidget;
  final Color? backgroundColor;

  const ImagePageView({
    super.key,
    required this.images,
    this.initialIndex = 0,
    this.controller,
    this.onPageChanged,
    this.onImageTap,
    this.fit = BoxFit.cover,
    this.enableInteractiveViewer = false,
    this.minScale = 0.5,
    this.maxScale = 3.0,
    this.borderRadius,
    this.placeholderAsset = 'assets/products/vegetables.png',
    this.loadingWidget,
    this.backgroundColor,
  });

  @override
  State<ImagePageView> createState() => ImagePageViewState();
}

class ImagePageViewState extends State<ImagePageView> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = widget.controller ?? PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    // Only dispose if we created the controller
    if (widget.controller == null) {
      _pageController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.images.isEmpty) {
      return _buildPlaceholderImage();
    }

    return Container(
      color: widget.backgroundColor,
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
          widget.onPageChanged?.call(index);
        },
        itemCount: widget.images.length,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: widget.onImageTap != null ? () => widget.onImageTap!(index) : null,
            child: _buildImageWidget(widget.images[index]),
          );
        },
      ),
    );
  }

  Widget _buildImageWidget(String imagePath) {
    Widget imageWidget = _buildImage(imagePath);

    if (widget.enableInteractiveViewer) {
      imageWidget = InteractiveViewer(
        minScale: widget.minScale,
        maxScale: widget.maxScale,
        child: imageWidget,
      );
    }

    if (widget.borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _buildImage(String imagePath) {
    return CustomImage(
      imageUrl: imagePath,
      fit: widget.fit,
      imageType: 'product',
      width: double.infinity,
      height: double.infinity,
    );
  }

  Widget _buildPlaceholderImage() {
    Widget placeholderWidget = CustomImage(
      imageUrl: widget.placeholderAsset,
      fit: widget.fit,
      imageType: 'product',
      width: double.infinity,
      height: double.infinity,
    );

    if (widget.onImageTap != null) {
      placeholderWidget = GestureDetector(
        onTap: () => widget.onImageTap!(0),
        child: placeholderWidget,
      );
    }

    return Container(
      color: widget.backgroundColor ?? Colors.grey[200],
      child: placeholderWidget,
    );
  }

  // / Get the current page index
  int get currentIndex => _currentIndex;

  /// Animate to a specific page
  Future<void> animateToPage(int page, {Duration? duration, Curve? curve}) {
    return _pageController.animateToPage(
      page,
      duration: duration ?? const Duration(milliseconds: 400),
      curve: curve ?? Curves.easeInOut,
    );
  }

  /// Jump to a specific page without animation
  void jumpToPage(int page) {
    _pageController.jumpToPage(page);
  }
}

/// A simple image widget that handles both network and asset images
/// Used for single images where PageView is not needed
class SimpleImageWidget extends StatelessWidget {
  final String imagePath;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final String? placeholderAsset;
  final Widget? loadingWidget;
  final Color? backgroundColor;

  const SimpleImageWidget({
    super.key,
    required this.imagePath,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholderAsset = 'assets/products/vegetables.png',
    this.loadingWidget,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = _buildImage();

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    return Container(
      color: backgroundColor,
      child: imageWidget,
    );
  }

  Widget _buildImage() {
    return CustomImage(
      imageUrl: imagePath,
      fit: fit,
      imageType: 'product',
      width: double.infinity,
      height: double.infinity,
    );
  }
}
