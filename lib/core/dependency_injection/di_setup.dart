import 'package:rozana/core/connectivity/bloc/connectivity_bloc.dart';
import 'package:rozana/core/services/token_refresh_service.dart';
import 'package:rozana/core/themes/theme_bloc/theme_bloc.dart';
import 'package:rozana/core/blocs/language_bloc/language_bloc.dart';
import 'package:rozana/data/repositories/auth_repository_impl.dart';
import 'package:rozana/data/repositories/home_repository_impl.dart';
import 'package:rozana/data/repositories/order_repository_impl.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/data/services/data_loading_manager.dart';

import 'package:rozana/domain/repositories/home_repository_interface.dart';
import 'package:rozana/domain/usecases/get_banners_usecase.dart';
import 'package:rozana/domain/usecases/get_categories_usecase.dart';
import 'package:rozana/domain/usecases/get_products_usecase.dart';

import 'package:rozana/domain/usecases/get_order_history_usecase.dart';
import 'package:rozana/domain/usecases/get_order_details_usecase.dart';
import 'package:rozana/domain/usecases/cancel_order_usecase.dart';
import 'package:rozana/domain/usecases/create_order_usecase.dart';
import 'package:rozana/domain/usecases/get_sub_categories_usecase.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/categories/bloc/categories_bloc.dart';
import 'package:rozana/features/order/bloc/order_bloc.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/features/location/services/adress_services.dart';
import 'package:rozana/features/location/services/locaion_services.dart';
import 'package:rozana/core/services/firestore_address_service.dart';
import 'package:rozana/features/profile/bloc/profile_bloc.dart';
import 'package:rozana/core/services/user_profile_service.dart';
import 'package:rozana/features/search/bloc/bloc/search_bloc.dart';
import 'package:rozana/features/search/services/typesense_service.dart';
import 'package:rozana/web-view/bloc/bloc/web_view_bloc.dart';

import '../../app/bloc/app_bloc.dart';
import '../../domain/repositories/auth_repository_interface.dart';
import '../../features/auth/bloc/login_bloc/login_bloc.dart';
import '../../features/home/<USER>/home bloc/home_bloc.dart';

// Import the global GetIt instance
import '../../features/products/bloc/product_listing_bloc.dart';
import '../utils/notifier.dart';
import 'di_container.dart';

void setupDI() {
  // Register Notifiers
  getIt.registerLazySingleton<AppNotifier>(
    () => NotificationServiceImpl(scaffoldMessengerKey),
  );

  // Register repositories (singletons as they often manage external resources)
  getIt.registerLazySingleton<IAuthRepository>(() => AuthRepositoryImpl());
  getIt.registerLazySingleton<OrderRepositoryInterface>(
      () => OrderRepositoryImpl());

  // Register TokenRefreshService for handling Firebase auth token refreshes
  getIt.registerLazySingleton<TokenRefreshService>(() => TokenRefreshService());
  // Register services (singletons as they often manage external resources)
  getIt.registerLazySingleton<LocationService>(
      () => LocationService()); //loaction service as singleton
  getIt.registerLazySingleton<FirestoreAddressService>(
      () => FirestoreAddressService()); //firestore address service as singleton
  getIt.registerLazySingleton<AddressService>(
      () => AddressService()); //address service as singleton
  getIt.registerLazySingleton<UserProfileService>(
      () => UserProfileService()); //user profile service as singleton
  getIt.registerLazySingleton<TypesenseService>(
      () => TypesenseService()); //typesense service as singleton
  getIt.registerLazySingleton<DataLoadingManager>(() => DataLoadingManager(
        typesenseService: getIt<TypesenseService>(),
      )); //Data loading manager with injected dependencies
  getIt.registerLazySingleton<IHomeRepository>(() => HomeRepositoryImpl(
      dataManager: getIt<
          DataLoadingManager>())); //Home repository with injected dependency

  // Register use cases (factories as they are stateless and can be created per use)
  getIt.registerFactory<GetCategoriesUseCase>(
      () => GetCategoriesUseCase(getIt<DataLoadingManager>()));
  getIt.registerFactory<GetSubCategoriesUseCase>(
      () => GetSubCategoriesUseCase(getIt<DataLoadingManager>()));
  getIt.registerFactory<GetProductsUseCase>(
      () => GetProductsUseCase(getIt<DataLoadingManager>()));
  getIt.registerFactory<GetBannersUseCase>(
      () => GetBannersUseCase(getIt<DataLoadingManager>()));

  // Register order use cases
  getIt.registerFactory<GetOrderHistoryUseCase>(
      () => GetOrderHistoryUseCase(getIt<OrderRepositoryInterface>()));
  getIt.registerFactory<GetOrderDetailsUseCase>(
      () => GetOrderDetailsUseCase(getIt<OrderRepositoryInterface>()));
  getIt.registerFactory<CancelOrderUseCase>(
      () => CancelOrderUseCase(getIt<OrderRepositoryInterface>()));
  getIt.registerFactory<CreateOrderUseCase>(
      () => CreateOrderUseCase(getIt<OrderRepositoryInterface>()));

  // Register BLoCs (typically as factories as they often have state and are created per-screen/per-widget)
  getIt.registerLazySingleton<ConnectivityBloc>(() => ConnectivityBloc());
  getIt.registerLazySingleton<AppBloc>(() => AppBloc());
  getIt.registerLazySingleton<ThemeBloc>(() => ThemeBloc());
  getIt.registerLazySingleton<LanguageBloc>(() => LanguageBloc());
  getIt.registerLazySingleton<LocationBloc>(() => LocationBloc(
      locationService: getIt<LocationService>(),
      addressService: getIt<AddressService>()));
  getIt.registerLazySingleton<CartBloc>(() => CartBloc(
        addressService: getIt<AddressService>(),
        createOrderUseCase: getIt<CreateOrderUseCase>(),
      ));
  getIt.registerLazySingleton<WebViewBloc>(() => WebViewBloc());
  getIt.registerFactory<LoginBloc>(() => LoginBloc(
        authRepository: getIt<IAuthRepository>(),
        notifier: getIt<AppNotifier>(),
        appBloc: getIt<AppBloc>(),
      ));
  getIt.registerFactory<HomeBloc>(() => HomeBloc(
        getSubCategoriesUseCase: getIt<GetSubCategoriesUseCase>(),
        getProductsUseCase: getIt<GetProductsUseCase>(),
        getBannersUseCase: getIt<GetBannersUseCase>(),
        categoriesUseCase: getIt<GetCategoriesUseCase>(),
      ));
  getIt
      .registerFactory<SearchBloc>(() => SearchBloc(getIt<TypesenseService>()));

  getIt.registerFactory<CategoriesBloc>(
      () => CategoriesBloc(getIt<GetCategoriesUseCase>()));
  getIt.registerFactory<ProductListingBloc>(() => ProductListingBloc(
        getProductsUseCase: getIt<GetProductsUseCase>(),
      ));
  getIt.registerFactory<ProfileBloc>(() => ProfileBloc(
        userProfileService: getIt<UserProfileService>(),
      ));

  // Register OrderBloc
  getIt.registerFactory<OrderBloc>(() => OrderBloc(
        getOrderHistoryUseCase: getIt<GetOrderHistoryUseCase>(),
        getOrderDetailsUseCase: getIt<GetOrderDetailsUseCase>(),
        cancelOrderUseCase: getIt<CancelOrderUseCase>(),
      ));
}
