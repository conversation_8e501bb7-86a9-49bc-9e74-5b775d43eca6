import 'dart:async';
import 'dart:convert';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../config/environment_config.dart';

class RemoteConfigService {
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;

  final Completer<void> _initializationCompleter = Completer<void>();

  Future<void> get initialized => _initializationCompleter.future;

  factory RemoteConfigService() => _instance;

  RemoteConfigService._internal();

  Future<void> initialize() async {
    try {
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 5),
        minimumFetchInterval:
            EnvironmentConfig.environment == Environment.development
                ? const Duration(minutes: 5)
                : const Duration(hours: 12),
      ));

      await _remoteConfig.fetchAndActivate().timeout(
        const Duration(seconds: 3),
        onTimeout: () {
          return false;
        },
      );

      if (!_initializationCompleter.isCompleted) {
        _initializationCompleter.complete();
      }
    } catch (e) {
      if (!_initializationCompleter.isCompleted) {
        _initializationCompleter.complete();
      }
    }
  }

  /// Get the environment config for the current environment
  Map<String, dynamic> get _currentEnvConfig {
    try {
      final String envKey = EnvironmentConfig.environment.name;
      final configValue = _remoteConfig.getValue(envKey);
      if (configValue.source == ValueSource.valueStatic) {
        return _getDefaultConfig();
      }
      final configString = configValue.asString();
      if (configString.isEmpty) {
        return _getDefaultConfig();
      }
      try {
        final Map<String, dynamic> parsedJson = json.decode(configString);
        return parsedJson;
      } catch (e) {
        return _getDefaultConfig();
      }
    } catch (e) {
      return _getDefaultConfig();
    }
  }

  /// Get the Typesense config from the environment config
  Map<String, dynamic> get _typesenseConfig {
    final envConfig = _currentEnvConfig;
    if (envConfig.containsKey('typesense')) {
      return envConfig['typesense'];
    } else {
      return _getDefaultTypesenseConfig();
    }
  }

  String get razorpayKey {
    final envConfig = _currentEnvConfig;
    return envConfig['razorpay_key'] ?? 'rzp_test_Jagi7su7aEjQ9P';
  }

  String get omsBaseUrl {
    final envConfig = _currentEnvConfig;
    return envConfig['omsBaseUrl'] ?? '';
  }

  String get imsBaseUrl {
    final envConfig = _currentEnvConfig;
    return envConfig['imsBaseUrl'] ?? '';
  }

  /// Get default config from the defaults JSON
  Map<String, dynamic> _getDefaultConfig() {
    return {
      "typesense": _getDefaultTypesenseConfig(),
      "razorpay_key": 'rzp_test_Jagi7su7aEjQ9P',
      "omsBaseUrl": '',
      "imsBaseUrl": ''
    };
  }

  /// Get default Typesense config as fallback
  Map<String, dynamic> _getDefaultTypesenseConfig() {
    return {
      "api_key": EnvironmentConfig.typesenseApiKey,
      "host": EnvironmentConfig.typesenseHost,
      "port": EnvironmentConfig.typesensePort,
      "protocol": EnvironmentConfig.typesenseProtocol,
      "collections": {
        "products": "facility_products",
        "categories": "store_categories",
        "stores": "stores",
        "top_products": "top_products"
      }
    };
  }

  /// Get the Typesense API key based on current environment
  String get typesenseApiKey => _typesenseConfig['api_key'];

  /// Get the Typesense host based on current environment
  String get typesenseHost => _typesenseConfig['host'];

  /// Get the Typesense port based on current environment
  String get typesensePort => _typesenseConfig['port'];

  /// Get the Typesense protocol based on current environment
  String get typesenseProtocol => _typesenseConfig['protocol'];

  /// Get collection names
  Map<String, dynamic> get _collections =>
      _typesenseConfig['collections'] ??
      {
        "products": "facility_products",
        "categories": "store_categories",
        "stores": "stores",
        "top_products": "top_products",
        "sliders": "sliders"
      };

  String get productsCollection =>
      _collections['products'] ?? 'facility_products';

  String get categoriesCollection =>
      _collections['categories'] ?? 'store_categories';

  String get storesCollection => _collections['stores'] ?? 'stores';

  String get topProductsCollection =>
      _collections['top_products'] ?? 'top_products';

  String get slidersCollection => _collections['sliders'] ?? 'sliders';
  String get sectionConfig =>
      _collections['section_config'] ?? 'section_config';
}
