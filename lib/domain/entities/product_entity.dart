/// Domain entity representing a product
/// Pure business object without external dependencies
class ProductEntity {
  final String id;
  final String name;
  final String description;
  final String? imageUrl;
  final List<String>? photos;
  final double price;
  final double? originalPrice;
  final double rating;
  final int reviewCount;
  final bool isInWishlist;
  final bool isOutOfStock;
  final String? discountLabel;
  final String category;
  final String? categoryId;
  final String? subcategory;
  final String? facilityId;
  final String? facilityName;
  final String? skuID;
  final String? brandId;
  final String? brandName;
  final int? availableQty;
  final int? maxLimit;
  final String? variantName;


  const ProductEntity({
    required this.id,
    required this.name,
    required this.description,
    this.imageUrl,
    this.photos,
    required this.price,
    this.originalPrice,
    required this.rating,
    required this.reviewCount,
    required this.isInWishlist,
    required this.isOutOfStock,
    this.discountLabel,
    required this.category,
    this.categoryId,
    this.subcategory,
    this.facilityId,
    this.facilityName,
    this.skuID,
    this.brandId,
    this.brandName,
    this.availableQty,
    this.maxLimit,
    this.variantName,
  });

  /// Calculate discount percentage
  double get discountPercentage {
    if (originalPrice == null || originalPrice! <= price) return 0.0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  /// Check if product has discount
  bool get hasDiscount => originalPrice != null && originalPrice! > price;

  /// Get savings amount
  double get savingsAmount {
    if (originalPrice == null || originalPrice! <= price) return 0.0;
    return originalPrice! - price;
  }

  /// Create a copy with updated values
  ProductEntity copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    List<String>? photos,
    double? price,
    double? originalPrice,
    double? rating,
    int? reviewCount,
    bool? isInWishlist,
    bool? isOutOfStock,
    String? discountLabel,
    String? category,
    String? categoryId,
    String? subcategory,
    String? facilityId,
    String? facilityName,
    String? skuID,
    String? brandId,
    String? brandName,
    int? availableQty,
    int? maxLimit,
    String? variantName,
  }) {
    return ProductEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      photos: photos ?? this.photos,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isInWishlist: isInWishlist ?? this.isInWishlist,
      isOutOfStock: isOutOfStock ?? this.isOutOfStock,
      discountLabel: discountLabel ?? this.discountLabel,
      category: category ?? this.category,
      categoryId: categoryId ?? this.categoryId,
      subcategory: subcategory ?? this.subcategory,
      facilityId: facilityId ?? this.facilityId,
      facilityName: facilityName ?? this.facilityName,
      skuID: skuID ?? this.skuID,
      brandId: brandId ?? this.brandId,
      brandName: brandName ?? this.brandName,
      availableQty: availableQty ?? this.availableQty,
      maxLimit: maxLimit ?? this.maxLimit,
      variantName: variantName ?? this.variantName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductEntity &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.imageUrl == imageUrl &&
        other.price == price &&
        other.originalPrice == originalPrice &&
        other.rating == rating &&
        other.reviewCount == reviewCount &&
        other.isInWishlist == isInWishlist &&
        other.isOutOfStock == isOutOfStock &&
        other.discountLabel == discountLabel &&
        other.category == category &&
        other.categoryId == categoryId &&
        other.subcategory == subcategory &&
        other.facilityId == facilityId &&
        other.facilityName == facilityName &&
        other.skuID == skuID &&
        other.brandId == brandId &&
        other.brandName == brandName &&
        other.availableQty == availableQty &&
        other.maxLimit == maxLimit
        && other.variantName == variantName;

  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        description.hashCode ^
        imageUrl.hashCode ^
        photos.hashCode ^
        price.hashCode ^
        originalPrice.hashCode ^
        rating.hashCode ^
        reviewCount.hashCode ^
        isInWishlist.hashCode ^
        isOutOfStock.hashCode ^
        discountLabel.hashCode ^
        category.hashCode ^
        categoryId.hashCode ^
        subcategory.hashCode ^
        facilityId.hashCode ^
        facilityName.hashCode ^
        skuID.hashCode ^

        brandId.hashCode ^
        brandName.hashCode ^
        availableQty.hashCode ^
        maxLimit.hashCode
        ^ variantName.hashCode;

  }

  @override
  String toString() {
    return 'ProductEntity(id: $id, name: $name, price: $price, category: $category, categoryId: $categoryId, subcategory: $subcategory, facilityId: $facilityId, facilityName: $facilityName, discountLabel: $discountLabel, rating: $rating, reviewCount: $reviewCount, isInWishlist: $isInWishlist, isOutOfStock: $isOutOfStock, originalPrice: $originalPrice, imageUrl: $imageUrl, skuID: $skuID, brandId: $brandId, brandName: $brandName, availableQty: $availableQty, maxLimit: $maxLimit, variantName: $variantName)';
  }
}
