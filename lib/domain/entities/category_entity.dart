/// Domain entity representing a product category
/// Pure business object without external dependencies
class CategoryEntity {
  final String id;
  final String name;
  final int? count;
  final String? imageUrl;
  final String? icon;
  final CategoryIconColor? iconColor;
  final String? parentID;
  final String? storeID;
  final String collectionId;
  final String? level;
  final bool? isAvailable;
  final String? page;

  const CategoryEntity({
    required this.id,
    required this.name,
    this.count,
    this.imageUrl,
    this.icon,
    this.iconColor,
    this.parentID,
    this.storeID,
    required this.collectionId,
    this.level,
    this.isAvailable,
    this.page,
  });

  /// Create a copy with updated values
  CategoryEntity copyWith({
    String? id,
    String? name,
    int? count,
    String? imageUrl,
    String? icon,
    CategoryIconColor? iconColor,
    String? parentID,
    String? storeID,
    String? collectionId,
    String? level,
    bool? isAvailable,
    String? page,
  }) {
    return CategoryEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      count: count ?? this.count,
      imageUrl: imageUrl ?? this.imageUrl,
      icon: icon ?? this.icon,
      iconColor: iconColor ?? this.iconColor,
      parentID: parentID ?? this.parentID,
      storeID: storeID ?? this.storeID,
      collectionId: collectionId ?? this.collectionId,
      level: level ?? this.level,
      isAvailable: isAvailable ?? this.isAvailable,
      page: page ?? this.page,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryEntity &&
        other.id == id &&
        other.name == name &&
        other.count == count &&
        other.imageUrl == imageUrl &&
        other.icon == icon &&
        other.iconColor == iconColor &&
        other.parentID == parentID &&
        other.storeID == storeID &&
        other.collectionId == collectionId &&
        other.level == level &&
        other.isAvailable == isAvailable &&
        other.page == page;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        count.hashCode ^
        imageUrl.hashCode ^
        icon.hashCode ^
        iconColor.hashCode ^
        parentID.hashCode ^
        storeID.hashCode ^
        collectionId.hashCode ^
        level.hashCode ^
        isAvailable.hashCode ^
        page.hashCode;
  }

  @override
  String toString() {
    return 'CategoryEntity(id: $id, name: $name, count: $count, imageUrl: $imageUrl, icon: $icon, iconColor: $iconColor, parentID: $parentID, storeID: $storeID, categoryId: $collectionId, level: $level, isAvailable: $isAvailable,)';
  }
}

/// Domain entity for category icon color
class CategoryIconColor {
  final int red;
  final int green;
  final int blue;
  final double opacity;

  const CategoryIconColor({
    required this.red,
    required this.green,
    required this.blue,
    this.opacity = 1.0,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryIconColor &&
        other.red == red &&
        other.green == green &&
        other.blue == blue &&
        other.opacity == opacity;
  }

  @override
  int get hashCode {
    return red.hashCode ^ green.hashCode ^ blue.hashCode ^ opacity.hashCode;
  }
}
