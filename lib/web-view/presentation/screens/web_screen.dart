import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/App/app.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/web-view/bloc/bloc/web_view_bloc.dart';
import 'package:rozana/web-view/presentation/widgets/company_info.dart';
import 'package:rozana/web-view/presentation/widgets/keyfeature.dart';
import 'package:rozana/web-view/presentation/widgets/mobile_frame.dart';
class WebScreen extends StatefulWidget {
  const WebScreen({super.key});

  @override
  State<WebScreen> createState() => _WebScreenState();
}

class _WebScreenState extends State<WebScreen> with AutomaticKeepAliveClientMixin {
  GoRouter? _mobileRouter;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Initialize the mobile router with optimized settings for web view
    _mobileRouter = _createOptimizedMobileRouter();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Create router here to ensure context is available
    _mobileRouter ??= _createOptimizedMobileRouter();
  }

  // Create a router that matches mobile behavior including splash and auth
  GoRouter _createOptimizedMobileRouter() {
    // Use the same router configuration as mobile
    return AppRouter.createRouter(context);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); 

    return BlocBuilder<WebViewBloc, WebViewState>(
      builder: (context, state) {
        return state.maybeWhen(
          loading: () => _buildMainScaffold(context),
          loaded: () => _buildMainScaffold(context),
          webViewMode: () => _buildMainScaffold(context),
          error: (message) => _buildErrorScaffold(context, message),
          orElse: () => _buildMainScaffold(context),
        );
      },
    );
  }



  Widget _buildMainScaffold(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 2,
        titleSpacing: 0,
        title: Row(
          children: [
            const SizedBox(width: 16),
            Text(
            'Rozana.in',
            style: TextStyle(color: Colors.black ,
            fontSize: 24,
            fontWeight: FontWeight.bold),
          ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CompanyInfo(),
                  KeyFeature(),

                ],
              ),
            ),
            Expanded(
              child: MobileFrame(
                child: _buildMobileApp(),
              ),
            ),
          ],
        ),

      ),
    );
  }

  Widget _buildMobileApp() {
    // Use the abstracted CoreAppWidget for the mobile frame
    // This reuses the exact same logic as the native mobile app
    return CoreAppWidget(
      router: _mobileRouter!,
      isWebFrame: true,
    );
  }

  Widget _buildErrorScaffold(BuildContext context, String message) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 2,
        titleSpacing: 0,
        title: Row(
          children: [
            const SizedBox(width: 16),
            Text(
              'Rozana.in',
              style: TextStyle(
                color: Colors.black,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading web view',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

}