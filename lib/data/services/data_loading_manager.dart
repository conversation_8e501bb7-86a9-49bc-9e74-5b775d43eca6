import 'dart:async';
import 'package:flutter/material.dart';

import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/category_entity.dart';
import '../../domain/entities/product_entity.dart';
import '../../features/search/services/typesense_service.dart';
import '../mappers/product_mapper.dart';

/// A manager class to handle data loading for the app
class DataLoadingManager {
  final TypesenseService _typesenseService;

  /// Constructor with dependency injection
  DataLoadingManager({
    required TypesenseService typesenseService,
  }) : _typesenseService = typesenseService;

  /// Load categories with pagination
  Future<List<CategoryEntity>> loadCategories({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
    String? secComponent,
  }) async {
    // Typesense uses 1-based pagination
    return await _typesenseService.searchCategories(
      query: query.isEmpty ? '*' : query,
      page: page + 1,
      pageSize: 50,
      secComponent: secComponent,
    );
  }

  /// Load subcategories with pagination
  Future<List<CategoryEntity>> loadSubCategories({
    CategoryEntity? category,
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
    String? level,
  }) async {
    // Always use Typesense
    return await _typesenseService.searchSubCategories(
      category: category,
      query: query.isEmpty ? '*' : query,
      page: page + 1,
      pageSize: pageSize,
      level: level,
    );
  }

  /// Load products with pagination
  Future<List<ProductEntity>> loadProducts({
    CategoryEntity? category,
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
    String? sectionType,
    bool? useParentId,
    bool? useCollectionId,
  }) async {
    // Always use Typesense
    final productsJson = await _typesenseService.searchProducts(
      category: category,
      query: query.isEmpty ? '*' : query,
      page: page + 1,
      pageSize: pageSize,
      useParentId: useParentId,
      useCollectionId: useCollectionId,
    );

    return ProductMapper.fromJsonList(productsJson);
  }

  Future<List<ProductEntity>> loadDynamicProducts({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
    String sectionType = '',
  }) async {
    // Always use Typesense
    try {
      final productsJson = await _typesenseService.dynamicProducts(
        query: query.isEmpty ? '*' : query,
        page: page + 1,
        pageSize: pageSize,
        sectionType: sectionType,
      );

      return ProductMapper.fromJsonList(productsJson);
    } catch (e) {
      return [];
    }
  }

  /// Load products by category with pagination
  Future<List<ProductEntity>> loadProductsByCategory({
    required String categoryId,
    int page = 0,
    int pageSize = 12,
    bool refresh = false,
    String query = '*',
    String? excludeProductId,
  }) async {
    return _loadProductsByFilter(
      categoryId: categoryId,
      page: page,
      pageSize: pageSize,
      refresh: refresh,
      query: query,
      excludeProductId: excludeProductId,
    );
  }

  /// Load featured products with pagination
  Future<List<ProductEntity>> loadFeaturedProducts({
    int page = 0,
    int pageSize = 9,
    bool refresh = false,
    String query = '',
  }) async {
    // Return empty list for now
    return [];
  }

  /// Load banners
  Future<List<BannerEntity>> getBanners({String? level}) async {
    return await _typesenseService.searchBanners(level: level);
  }

  /// Load orders with pagination - placeholder for future implementation
  Future<List<Map<String, dynamic>>> loadOrders({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  }) async {
    // This would be implemented with Typesense when orders collection is available
    return [];
  }

  /// Load order details by ID - placeholder for future implementation
  Future<Map<String, dynamic>?> loadOrderDetails(String orderId) async {
    // This would be implemented with Typesense when orders collection is available
    return null;
  }

  /// Load products by brand with pagination
  Future<List<ProductEntity>> loadProductsByBrand({
    required String brandId,
    int page = 0,
    int pageSize = 12,
    bool refresh = false,
    String query = '*',
    String? excludeProductId,
  }) async {
    return _loadProductsByFilter(
      brandId: brandId,
      page: page,
      pageSize: pageSize,
      refresh: refresh,
      query: query,
      excludeProductId: excludeProductId,
    );
  }

  /// Generic method to load products by different filter types (category, brand, etc.)
  Future<List<ProductEntity>> _loadProductsByFilter({
    String? categoryId,
    String? brandId,
    int page = 0,
    int pageSize = 12,
    bool refresh = false,
    String query = '*',
    String? excludeProductId,
  }) async {
    // Typesense uses 1-based pagination
    final productsJson = await _typesenseService.searchProducts(
        categoryId: categoryId,
        brandId: brandId,
        query: query,
        page: page + 1,
        pageSize: pageSize,
        excludeProductId: excludeProductId);

    return ProductMapper.fromJsonList(productsJson);
  }
}

class DataLoadingProvider extends InheritedWidget {
  final DataLoadingManager dataLoadingManager;

  const DataLoadingProvider({
    super.key,
    required this.dataLoadingManager,
    required super.child,
  });

  static DataLoadingManager of(BuildContext context) {
    final provider =
        context.dependOnInheritedWidgetOfExactType<DataLoadingProvider>();
    assert(provider != null, 'No DataLoadingProvider found in context');
    return provider!.dataLoadingManager;
  }

  @override
  bool updateShouldNotify(DataLoadingProvider oldWidget) {
    return dataLoadingManager != oldWidget.dataLoadingManager;
  }
}
