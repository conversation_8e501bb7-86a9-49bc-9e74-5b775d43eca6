import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:rozana/domain/entities/banner_entity.dart';
import 'package:typesense/typesense.dart';
import '../../../core/blocs/language_bloc/language_bloc.dart';
import '../../../core/dependency_injection/di_container.dart';
import '../../../core/services/remote_config_service.dart';
import '../../../domain/entities/category_entity.dart';

class TypesenseConfig {
  static final RemoteConfigService _remoteConfig = RemoteConfigService();

  static String get apiKey => _remoteConfig.typesenseApiKey;

  static String get host => _remoteConfig.typesenseHost;

  static String get port => _remoteConfig.typesensePort;

  static String get protocol => _remoteConfig.typesenseProtocol;

  static const int connectionTimeoutSeconds = 10;

  static String get productsCollection => _remoteConfig.productsCollection;

  static String get categoriesCollection => _remoteConfig.categoriesCollection;

  static String get storesCollection => _remoteConfig.storesCollection;

  static String get topProductsCollection =>
      _remoteConfig.topProductsCollection;

  static String get slidersCollection => _remoteConfig.slidersCollection;

  static String get sectionConfig => _remoteConfig.sectionConfig;
}

class TypesenseService {
  late final Client _client;

  String? _currentStoreId;
  String? _currentStoreName;
  bool _isLocationServiceable = true;

  Completer<void> _storeIdInitialized = Completer<void>();

  bool _initializationAttempted = false;

  static final TypesenseService _instance = TypesenseService._internal();

  factory TypesenseService() => _instance;

  TypesenseService._internal() {
    _initializeClient();
  }

  /// Get the current store ID
  String? get currentStoreId => _currentStoreId;
  String? get currentStoreName => _currentStoreName;

  /// Check if the current location is serviceable
  bool get isLocationServiceable => _isLocationServiceable;

  /// Returns a Future that completes when the store ID is initialized
  Future<void> get storeIdInitialized => _storeIdInitialized.future;

  /// Initialize the store ID using coordinates
  Future<void> initializeWithCoordinates(
    double latitude,
    double longitude,
  ) async {
    if (_initializationAttempted) {
      return;
    }

    _initializationAttempted = true;

    try {
      final store = await findStoreByCoordinates(latitude, longitude);
      if (store != null) {
        _currentStoreId = store['id']?.toString();
        _currentStoreName = store['name']?.toString();
        _isLocationServiceable = true;
      } else {
        // Location is not serviceable
        _isLocationServiceable = false;
        _currentStoreId = null;
        _currentStoreName = null;
      }
    } catch (e) {
      debugPrint('TypesenseService: Error initializing with coordinates: $e');
      _isLocationServiceable = false;
      _currentStoreId = null;
      _currentStoreName = null;
    }

    // Complete the future to signal that store ID is initialized
    if (!_storeIdInitialized.isCompleted) {
      _storeIdInitialized.complete();
      debugPrint(
        'TypesenseService: Store ID initialization completed. Location serviceable: $_isLocationServiceable',
      );
    }
  }

  /// Reinitialize the store ID using coordinates (for address changes)
  /// This method resets the initialization state and reinitializes with new coordinates
  Future<void> reinitializeWithCoordinates(
    double latitude,
    double longitude,
  ) async {
    debugPrint(
      'TypesenseService: Reinitializing with coordinates: $latitude, $longitude',
    );

    // Reset initialization state
    _initializationAttempted = false;

    // Create a new completer if the previous one was completed
    if (_storeIdInitialized.isCompleted) {
      _storeIdInitialized = Completer<void>();
    }

    // Initialize with the new coordinates
    await initializeWithCoordinates(latitude, longitude);

    debugPrint(
      'TypesenseService: Reinitialization completed with store ID: $_currentStoreId',
    );
  }

  /// Find store based on coordinates
  Future<Map<String, dynamic>?> findStoreByCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      debugPrint('TypesenseService: Searching for store at coordinates: $latitude, $longitude');

      // First, let's get all stores to debug
      final allStoresParameters = {
        'q': '*',
        'per_page': '10',
      };

      final allStoresResult = await _client
          .collection(TypesenseConfig.storesCollection)
          .documents
          .search(allStoresParameters);

      final allHits = allStoresResult['hits'] as List<dynamic>?;
      debugPrint('TypesenseService: Found ${allHits?.length ?? 0} total stores');

      if (allHits != null && allHits.isNotEmpty) {
        for (int i = 0; i < allHits.length; i++) {
          final store = allHits[i]['document'] as Map<String, dynamic>;
          debugPrint('TypesenseService: Store $i - ID: ${store['id']}, Name: ${store['name']}, Polygon: ${store['polygon_coords']}');
        }
      }

      // Now search with polygon filter
      final searchParameters = {
        'q': '*',
        'filter_by': 'polygon_coords:($latitude,$longitude)',
        'per_page': '1',
      };

      debugPrint('TypesenseService: Searching with filter: ${searchParameters['filter_by']}');

      final searchResult = await _client
          .collection(TypesenseConfig.storesCollection)
          .documents
          .search(searchParameters);

      final hits = searchResult['hits'] as List<dynamic>?;
      debugPrint('TypesenseService: Polygon search returned ${hits?.length ?? 0} stores');

      if (hits == null || hits.isEmpty) {
        debugPrint('TypesenseService: No stores found for coordinates: $latitude, $longitude');
        return null;
      }

      final storeDocument = hits.first['document'] as Map<String, dynamic>;
      debugPrint('TypesenseService: Found matching store: ${storeDocument['id']} - ${storeDocument['name']}');
      return storeDocument;
    } catch (e) {
      debugPrint('TypesenseService: Error finding store by coordinates: $e');
      return null;
    }
  }

  /// Initialize the Typesense client
  void _initializeClient() {
    _client = Client(
      Configuration(
        TypesenseConfig.apiKey,
        nodes: {
          Node(
            TypesenseConfig.protocol == 'https'
                ? Protocol.https
                : Protocol.http,
            TypesenseConfig.host,
          ),
        },
        connectionTimeout: Duration(
          seconds: TypesenseConfig.connectionTimeoutSeconds,
        ),
        numRetries: 3,
      ),
    );
  }

  /// Search for categories
  Future<List<CategoryEntity>> searchCategories({
    String query = '*',
    int page = 1,
    int pageSize = 20,
    String? secComponent,
  }) async {
    try {
      // Wait for store ID to be initialized
      await storeIdInitialized;
      List<String> filters = [];
      final Map<String, String> searchParameters = {
        'q': query,
        'page': page.toString(),
        'per_page': pageSize.toString(),
        'sort_by': 'position:asc',
      };
      if (_currentStoreId != null) {
        filters.add('store_id:$_currentStoreId');
      }

      if (secComponent == null) {
        filters.add('parent_id:0');
        filters.add('status:true');
      } else {
        filters.add('display:=true');
        filters.add('sec_comp:=$secComponent');
      }

      if (filters.isNotEmpty) {
        searchParameters['filter_by'] = filters.join('&&');
      }

      final searchResult = await _client
          .collection(secComponent != null
              ? TypesenseConfig.sectionConfig
              : TypesenseConfig.categoriesCollection)
          .documents
          .search(searchParameters);
      return _processStoreCategoryResults(searchResult);
    } catch (e) {
      debugPrint('Error searching categories: $e');
      return [];
    }
  }

  Future<Map<String, List<dynamic>>> getDataByDynamicQuery({
    String query = '',
    int page = 1,
    int pageSize = 20,
    String queryCollection = '',
  }) async {
    try {
      // Wait for store ID to be initialized
      await storeIdInitialized;
      final Map<String, dynamic> searchParameters = jsonDecode(query) ?? {};
      List<String> filters = [
        (searchParameters['filter_by'])?.toString() ?? ''
      ];

      searchParameters['page'] = page.toString();
      searchParameters['per_page'] = pageSize.toString();

      if (_currentStoreId != null) {
        filters.insert(0, 'store_id:$_currentStoreId');
      }

      if (filters.isNotEmpty) {
        searchParameters['filter_by'] = filters.join('&&');
      }

      final searchResult = await _client
          .collection(queryCollection)
          .documents
          .search(searchParameters);

      final products = _processProductResults(searchResult);
      final categories = _processStoreCategoryResults(searchResult);

      return {'products': products, 'categories': categories};
    } catch (e) {
      debugPrint('Error searching categories: $e');
      return {'products': [], 'categories': [], 'subcategories': []};
    }
  }

  /// Search for subcategories by category
  Future<List<CategoryEntity>> searchSubCategories({
    CategoryEntity? category,
    String query = '*',
    int page = 1,
    int pageSize = 250,
    String? level,
    bool useCollectionId = false,
  }) async {
    try {
      // Wait for store ID to be initialized
      await storeIdInitialized;

      final Map<String, String> searchParameters = {
        'q': query,
        'page': page.toString(),
        'per_page': pageSize.toString(),
        'sort_by': 'position:asc',
      };

      List<String> filters = [];
      // Add store filter if available
      if (_currentStoreId != null) {
        filters.add('store_id:$_currentStoreId');
      }

      // Add parent category filter if available
      if (category != null) {
        if (useCollectionId) {
          filters.add('collection_id:${category.collectionId}');
        } else {
          filters.add('parent_id:${category.collectionId}');
        }
      }

      // Add level filter if provided
      if (level != null && level.isNotEmpty) {
        filters.add('level:=$level');
      }

      // Add status filter
      filters.add('status:true');

      // Combine all filters
      if (filters.isNotEmpty) {
        searchParameters['filter_by'] = filters.join('&&');
      }

      final searchResult = await _client
          .collection(TypesenseConfig.categoriesCollection)
          .documents
          .search(searchParameters);

      return _processStoreCategoryResults(searchResult);
    } catch (e) {
      debugPrint('Error searching subcategories: $e');
      return [];
    }
  }

  /// Search for products
  Future<List<Map<String, dynamic>>> searchProducts(
      {String query = '*',
      int page = 1,
      int pageSize = 20,
      CategoryEntity? category,
      String? categoryId,
      String? brandId,
      String? sortBy,
      List<String>? skus,
      bool? sortDescending,
      bool? useParentId,
      String? excludeProductId,
      bool? useCollectionId}) async {
    try {
      // Wait for store ID to be initialized
      await storeIdInitialized;

      final Map<String, String> searchParameters = {
        'q': query,
        'page': page.toString(),
        'per_page': pageSize.toString(),
        'group_by': 'sku',
        'group_limit': '1'
      };

      List<String> filters = [];
      // Add store filter if available
      if (_currentStoreId != null) {
        filters.add('store_id:=$_currentStoreId');
      }

      filters.add('available_qty:=>0');
      filters.add('is_available:=true');

      // Add category filter if provided directly
      if (categoryId != null) {
        filters.add('category_id:=$categoryId');
      }

      // Add brand filter if provided
      if (brandId != null) {
        filters.add('brand_id:=$brandId');
      }

      // Add category/subcategory filter based on parentID
      if (category != null) {
        if (useParentId ?? false) {
          filters.add('parent_id:=${category.collectionId}');
        } else {
          if (useCollectionId ?? false) {
            filters.add('collection_id:=${category.collectionId}');
          } else {
            filters.add('category_id:=${category.collectionId}');
          }
        }
      }

      if (skus != null && skus.isNotEmpty) {
        final childSkuFilter = "child_sku:=['${skus.join("','")}']";
        filters.add(childSkuFilter);
      }

      if (excludeProductId != null) {
        filters.add('id:!=$excludeProductId');
      }

      // Combine all filters
      if (filters.isNotEmpty) {
        searchParameters['filter_by'] = filters.join('&&');
      }

      final searchResult = await _client
          .collection(TypesenseConfig.productsCollection)
          .documents
          .search(searchParameters);

      return _processProductResults(searchResult);
    } catch (e) {
      debugPrint('Error searching products: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> dynamicProducts({
    String query = '*',
    int page = 1,
    int pageSize = 20,
    String? sortBy,
    bool? sortDescending,
    String? sectionType,
  }) async {
    try {
      // Wait for store ID to be initialized
      await storeIdInitialized;

      final Map<String, String> searchParameters = {
        'q': query,
        'page': page.toString(),
        'per_page': pageSize.toString(),
        'group_by': 'sku',
        'group_limit': '1'
      };

      List<String> filters = [];
      // Add store filter if available
      if (_currentStoreId != null) {
        filters.add('store_id:=$_currentStoreId');
        filters.add('is_available:=true');
      }

      // filters.add('available_qty:=>0');

      // Add section type filter if available
      // Using tag field instead of section_type since section_type doesn't exist in schema
      if (sectionType != null && sectionType.isNotEmpty) {
        // Try using tags field which is more commonly available in Typesense schemas
        filters.add('section_type:=$sectionType');
      }

      // Combine all filters
      if (filters.isNotEmpty) {
        searchParameters['filter_by'] = filters.join('&&');
      }

      final searchResult = await _client
          .collection(TypesenseConfig.topProductsCollection)
          .documents
          .search(searchParameters);

      return _processProductResults(searchResult);
    } catch (e) {
      debugPrint('Error searching products: $e');
      return [];
    }
  }

  /// Global search across products, categories, and subcategories
  Future<Map<String, List<Map<String, dynamic>>>> globalSearch({
    required String query,
    int page = 1,
    int pageSize = 20,
    String? sku,
  }) async {
    if (query.isEmpty) {
      return {'products': [], 'categories': [], 'subcategories': []};
    }

    try {
      final searchParameters = {
        "q": query,
        "query_by": "name,brand_name,tags",
        "query_by_weights": "10,8,12",
        "filter_by":
            "store_id:$_currentStoreId && is_available:=true && available_qty:>0",
        "num_typos": '2',
        "split_join_tokens": "fallback",
        "match_all_tokens": 'true',
        "prefix": 'true',
        "page": page.toString(),
        "per_page": pageSize.toString(),
        "group_by": "sku",
        "group_limit": '1',
        "prioritize_token_position": 'true',
        "prioritize_exact_match": 'true',
        "sort_by": "_text_match(bucket_size: 7):desc",
        "exhaustive_search": 'true',
        "drop_tokens_threshold": '1'
      };

      if ((sku ?? '').isNotEmpty) {
        searchParameters['sku'] = sku ?? '';
      }

      final searchResult = await _client
          .collection(TypesenseConfig.productsCollection)
          .documents
          .search(searchParameters);

      final products = _processProductResults(searchResult);

      return {'products': products, 'categories': [], 'subcategories': []};
    } catch (e) {
      debugPrint('Error in global search: $e');
      return {'products': [], 'categories': [], 'subcategories': []};
    }
  }

  /// Get search suggestions based on user input
  Future<List<Map<String, dynamic>>> getSearchSuggestions(String query) async {
    if (query.isEmpty) {
      return [];
    }

    Map<String, dynamic> searchQuery = {
      "q": query,
      "query_by": "name,brand_name,tags",
      "query_by_weights": "10,8,12",
      "filter_by":
          "store_id:$_currentStoreId && is_available:=true && available_qty:>0",
      "num_typos": '2',
      "split_join_tokens": "fallback",
      "match_all_tokens": 'true',
      "prefix": 'true',
      "per_page": '7',
      "group_by": "sku",
      "group_limit": '1',
      "prioritize_token_position": 'true',
      "prioritize_exact_match": 'true',
      "sort_by": "_text_match(bucket_size: 7):desc",
      "exhaustive_search": 'true',
      "drop_tokens_threshold": '1'
    };

    try {
      final searchResult = await _client
          .collection(TypesenseConfig.productsCollection)
          .documents
          .search(searchQuery);

      final data = _processProductResults(searchResult);

      // Extract product names as suggestions from processed data
      final suggestions = data
          .map(
            (product) => {
              'name': product['display_alias'] ??
                  product['display_name'] ??
                  product['name'],
              'imageUrl': product['imageUrl'],
              'sku': product['sku'] ?? '',
            },
          )
          .toList();

      // Add the original query as the first suggestion
      if (!suggestions.any((suggestion) => suggestion['name'] == query)) {
        suggestions.insert(0, {
          'name': query,
          'imageUrl': 'assets/products/search.png',
        });
      }

      return suggestions;
    } catch (e) {
      debugPrint('Error getting search suggestions: $e');
      return [
        {'name': query, 'imageUrl': 'assets/products/search.png'},
      ]; // Return the original query as fallback
    }
  }

  /// Process store category search results
  List<CategoryEntity> _processStoreCategoryResults(
    Map<String, dynamic> searchResult,
  ) {
    final hits = searchResult['hits'] as List<dynamic>?;
    if (hits == null) return [];

    return hits.map((hit) {
      final document = hit['document'] as Map<String, dynamic>;
      return CategoryEntity(
        id: document['id'] ?? '',
        name: document['name'] ?? document['title'] ?? '',
        count: 0,
        imageUrl: document['icon'] ??
            document['image'] ??
            document['thumbnail_image'] ??
            '',
        icon: document['icon'] ?? '',
        parentID: document['parent_id'] ?? '',
        storeID: document['store_id'] ?? _currentStoreId,
        collectionId:
            document['collection_id'] ?? document['category_id'] ?? '',
        isAvailable: document['available'],
        page: document['page'],
        displayPage: document['display_page'],
        queryCollection: document['query_collection'],
        query: document['query'],
      );
    }).toList();
  }

  /// Process product search results from Typesense
  List<Map<String, dynamic>> _processProductResults(
    Map<String, dynamic> searchResult,
  ) {
    final groupedHits = searchResult['grouped_hits'] as List<dynamic>?;
    final hits = searchResult['hits'] as List<dynamic>?;

    // Collect all products from all groups
    final List<Map<String, dynamic>> allProducts = [];

    if (groupedHits != null && groupedHits.isNotEmpty) {
      for (final group in groupedHits) {
        final hits = group['hits'] as List<dynamic>?;
        if (hits == null || hits.isEmpty) {
          continue;
        }

        for (final hit in hits) {
          final document = hit['document'] as Map<String, dynamic>;
          allProducts.add(_getProductMap(document));
        }
      }
    } else if (hits != null && hits.isNotEmpty) {
      for (final hit in hits) {
        final document = hit['document'] as Map<String, dynamic>;
        allProducts.add(_getProductMap(document));
      }
    }

    return allProducts;
  }

  Map<String, dynamic> _getProductMap(Map<String, dynamic> document) {
    return {
      'id': document['id'] ?? '',
      'objectID': document['id'] ?? '',
      'name': document['name'] ?? '',
      'display_name': document['display_name'],
      'display_alias': document['display_alias'],
      'description': document['description'] ?? '',
      'imageUrl': document['thumbnail_image'] ??
          document['thumbnail_img'] ??
          document['photos']?[0] ??
          'assets/products/vegetables.png',
      'photos': document['photos'] is List
          ? List<String>.from(document['photos'])
          : <String>[],
      'price': document['price'] ?? document['selling_price'] ?? 0.0,
      'originalPrice': document['mrp'] ?? 0.0,
      'rating': document['rating'] ?? 0.0,
      'reviewCount': document['review_count'] ?? 0,
      'isInWishlist': false,
      'isOutOfStock':
          !(document['is_available'] ?? true), // Fix: invert the logic
      'categoryId': document['category_id'] ?? '',
      'brand_id': document['brand_id'] ?? '',
      'brand_name': document['brand_name'] ?? '',
      'categoryName': document['category_name'] ?? '',
      'discountLabel': null,
      'category': document['category_name'] ?? '',
      'subcategory': document['subcategory_name'] ?? '',
      'facilityId': document['facility_code'] ?? '',
      'facilityName': document['facility_code'] ?? document['facility_name'] ?? '',
      'sku': document['child_sku'] ??
          document['sku'] ??
          document['variant_id'] ??
          '',
      'product_id': document['product_id'] ?? '',
      'variant_name': document['variant_name'] ?? '',
      'max_purchase_limit': document['max_purchase_limit'] ?? 0,
      'available_qty': document['available_qty'] ?? 0,
      'store_id': document['store_id'] ?? '',
    };
  }

  /// Search for banners
  Future<List<BannerEntity>> searchBanners({
    int page = 1,
    int pageSize = 10,
    String? level,
  }) async {
    try {
      // Wait for store ID to be initialized
      await storeIdInitialized;

      // Get current language from LanguageBloc
      final languageBloc = getIt<LanguageBloc>();
      final currentLanguage =
          languageBloc.currentLanguageCode == 'en' ? 'english' : 'hindi';

      final Map<String, String> searchParameters = {
        'q': '*',
        'page': page.toString(),
        'per_page': pageSize.toString(),
      };

      List<String> filters = [];

      // Add store ID filter if available

      if (_currentStoreId != null) {
        filters.add('store_id:$_currentStoreId&&language:$currentLanguage');
      }
      if (level != null) {
        filters.add('level:=$level');
      }

      if (filters.isNotEmpty) {
        searchParameters['filter_by'] = filters.join('&&');
      }

      final searchResult = await _client
          .collection(TypesenseConfig.slidersCollection)
          .documents
          .search(searchParameters);

      return _processBannerResults(searchResult);
    } catch (e) {
      debugPrint('Error searching banners: $e');
      return [];
    }
  }

  /// Process banner search results from Typesense
  List<BannerEntity> _processBannerResults(Map<String, dynamic> searchResult) {
    final hits = searchResult['hits'] as List<dynamic>?;
    if (hits == null || hits.isEmpty) {
      return [];
    }

    final List<BannerEntity> processedResults = [];
    for (var hit in hits) {
      final document = hit['document'] as Map<String, dynamic>;
      processedResults.add(
        BannerEntity(
          id: document['id']?.toString() ?? '',
          imageUrl: document['banner']?.toString() ?? '',
          collectionId: document['sub_category_id']?.toString() ?? '',
          name: document['name']?.toString() ?? '',
        ),
      );
    }
    return processedResults;
  }
}
