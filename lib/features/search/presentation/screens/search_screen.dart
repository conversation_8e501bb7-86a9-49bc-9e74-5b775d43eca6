import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/features/products/presentation/widgets/product_section.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_textfield.dart';

import '../../../../data/models/product_model.dart';
import '../../../cart/presentation/widgets/floating_cart_wrapper.dart';
import '../../bloc/bloc/search_bloc.dart';

class SearchScreen extends StatelessWidget {
  const SearchScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    SearchBloc searchBloc = context.read<SearchBloc>();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        titleSpacing: 0,
        automaticallyImplyLeading: false, // Remove default leading
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: SearchTextField(
            controller: SearchBloc.searchFieldManager?.controller,
            focusNode: SearchBloc.searchFieldManager?.focusNode,
            hintText: 'Search...',
            autofocus: true,
            onChanged: (query) =>
                searchBloc.add(SearchEvent.inputChange(query)),
            onSubmitted: (query) =>
                searchBloc.add(SearchEvent.search(query, submit: true)),
            onClear: () => searchBloc.add(const SearchEvent.clearSearch()),
            onBackPressed: () => context.pop(),
          ),
        ),
      ),
      body: BlocBuilder<SearchBloc, SearchState>(
        builder: (context, state) {
          return NotificationListener<ScrollNotification>(
            onNotification: (scrollInfo) {
              if ((scrollInfo.metrics.maxScrollExtent > 0) &&
                  (scrollInfo.metrics.pixels >=
                      scrollInfo.metrics.maxScrollExtent * 0.8) &&
                  state.hasMore &&
                  !state.isLoading) {
                searchBloc.add(const SearchEvent.loadMore());
              }
              return false;
            },
            child: SearchBody(state: state),
          );
        },
      ),
    );
  }
}

class SearchBody extends StatelessWidget {
  const SearchBody({super.key, required this.state});

  final SearchState state;

  @override
  Widget build(BuildContext context) {
    SearchBloc searchBloc = context.read<SearchBloc>();
    if (state.suggestions.isNotEmpty) {
      return ListView.builder(
        itemCount: state.suggestions.length,
        itemBuilder: (context, index) {
          final suggestion = state.suggestions[index];
          return ListTile(
            leading: CustomImage(
                imageUrl: suggestion['imageUrl'],
                width: 50,
                height: 50,
                imageType: 'product'),
            title: Text(suggestion['display_alias'] ?? suggestion['name'],
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 14)),
            onTap: () => context.read<SearchBloc>().add(
                SearchEvent.selectSuggestion(
                    suggestion['name'], suggestion['sku'])),
          );
        },
      );
    }

    if (state.products.isEmpty && state.query.isEmpty) {
      if (state.recentSearches.isEmpty) {
        return const Center(
          child: Text('No recent searches'),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Recent Searches',
                    style:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                TextButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    context
                        .read<SearchBloc>()
                        .add(const SearchEvent.clearRecent());
                  },
                  child: const Text('Clear'),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: state.recentSearches.length,
              itemBuilder: (context, index) {
                final recentSearch = state.recentSearches[index];
                return ListTile(
                  leading:
                      const Icon(Icons.history, size: 20, color: Colors.grey),
                  title:
                      Text(recentSearch, style: const TextStyle(fontSize: 14)),
                  trailing: IconButton(
                    icon: const Icon(Icons.close, size: 16),
                    onPressed: () {
                      searchBloc.add(SearchEvent.removeRecent(index));
                    },
                  ),
                  onTap: () {
                    context
                        .read<SearchBloc>()
                        .add(SearchEvent.selectRecent(recentSearch));
                  },
                );
              },
            ),
          ),
        ],
      );
    }

    if (state.isLoading && state.products.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (!state.isLoading && state.products.isEmpty && state.query.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.arrow_back, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No results found for "${state.query}"',
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // return SizedBox();
    return SearchResult(
      state: state,
    );
  }
}

class SearchResult extends StatelessWidget {
  const SearchResult({super.key, required this.state});

  final SearchState state;

  final bool _showCategories = false;
  final bool _showSubcategories = false;

  @override
  Widget build(BuildContext context) {
    return FloatingCartWrapper(
      // excludeFloatingCart: isCartScreen,
      excludeOrderTile: true,
      bottomPadding: 0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Text(
              'Results for "${state.query}"',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Categories section (if any)
          if (_showCategories && state.categories.isNotEmpty)
            // _buildCategoriesSection(),

            // Debug text to show category count
            if (_showCategories && state.categories.isNotEmpty)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Found ${state.categories.length} categories',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ),

          // Subcategories section (if any)
          if (_showSubcategories &&
              state.subcategories.isNotEmpty &&
              state.categories.isNotEmpty)
            const SizedBox(height: 16),

          if (_showSubcategories && state.subcategories.isNotEmpty)
            // _buildSubcategoriesSection(),

            // Debug text to show subcategory count
            if (_showSubcategories && state.subcategories.isNotEmpty)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Found ${state.categories.length} subcategories',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ),

          // Products section
          if (state.products.isNotEmpty)
            Expanded(
                child: ProductsSection(
              title: 'Products',
              onSeeAllTap: () {}, // Already showing all products
              preloadData: false, // We'll provide our own data
              showSeeAll: false,
              useGridView: true,
              imageHeight: 80,
              gridCrossAxisCount: 3,
              gridChildAspectRatio: 0.55,
              scrollController: SearchBloc.scrollController,
              // // Pass search results as external products
              bottomPadding: 100,
              externalProducts: state.products
                  .map((json) => ProductModel.fromJson(json))
                  .toList(),
              // // Pass load more callback for pagination only if enabled
              onLoadMore: () {
                if (SearchBloc.enableSearchPagination) {
                  if (context.mounted) {
                    context.read<SearchBloc>().add(SearchEvent.loadMore());
                  }
                }
              },
              onProductTap: (product) {
                // // Navigate to product detail
                HapticFeedback.lightImpact();
                context.push(RouteNames.productDetail,
                    extra: {'product': product});
              },
            )),

          // No results message
          if (state.products.isEmpty &&
              state.categories.isEmpty &&
              state.subcategories.isEmpty &&
              !state.isLoading &&
              state.query.isNotEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.search_off,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No results found for "${state.query}"',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Try a different search term or browse categories',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
