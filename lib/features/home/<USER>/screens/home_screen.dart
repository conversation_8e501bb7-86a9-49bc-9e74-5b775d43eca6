import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/cart/presentation/widgets/floating_cart_wrapper.dart';
import 'package:rozana/features/categories/bloc/categories_bloc.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_text.dart';
import '../../bloc/home bloc/home_bloc.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key, required this.child});

  final Widget child;

  int _locationToIndex(String location) {
    if (location.startsWith(RouteNames.home)) return 0;
    if (location.startsWith(RouteNames.categories)) return 1;
    if (location.startsWith(RouteNames.orders)) return 2;
    if (location.startsWith(RouteNames.cart)) return 3;
    return 0;
  }

  void pushNewRoute(BuildContext context, int prev, String route) {
    if (prev == 0) {
      context.push(route);
    } else {
      context.replace(route);
    }
  }

  @override
  Widget build(BuildContext context) {
    String location = GoRouterState.of(context).uri.toString();
    // Check if current screen is cart screen to exclude floating cart button
    bool isCartScreen = location.startsWith(RouteNames.cart);
    bool isOrderScreen = location.startsWith(RouteNames.orders);

    return BlocListener<HomeBloc, HomeState>(
      listenWhen: (previous, current) => (current is HomeDeepLink),
      listener: (context, state) {
        state.mapOrNull(
          deepLink: (value) {
            context.push(value.route, extra: value.args);
          },
        );
      },
      child: Scaffold(
        body: BlocBuilder<HomeBloc, HomeState>(
          buildWhen: (previous, current) =>
              (previous is! HomeLoaded) && (current is HomeLoaded),
          builder: (context, homeState) {
            return FloatingCartWrapper(
              excludeFloatingCart: isCartScreen || (homeState is! HomeLoaded),
              excludeOrderTile:
                  isCartScreen || isOrderScreen || (homeState is! HomeLoaded),
              bottomPadding: 0,
              child: NotificationListener<UserScrollNotification>(
                onNotification: (notification) {
                  if (notification.direction == ScrollDirection.reverse) {
                    // Dispatch event to Bloc to hide the bar
                    context.read<HomeBloc>().add(
                        const HomeEvent.scrollDirectionChanged(
                            ScrollDirection.reverse));
                  } else if (notification.direction ==
                      ScrollDirection.forward) {
                    // Dispatch event to Bloc to show the bar
                    context.read<HomeBloc>().add(
                        const HomeEvent.scrollDirectionChanged(
                            ScrollDirection.forward));
                  }
                  return false; // Don't block other notifications
                },
                child: child, // The actual screen content
              ),
            );
          },
        ),

        // bottomNavigationBar: bottomNavigationBar(context),
        bottomNavigationBar: BlocSelector<HomeBloc, HomeState, bool>(
            selector: (state) => state.showBottomNavBar,
            builder: (context, showBottomNavBar) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: showBottomNavBar
                    ? 73 + MediaQuery.of(context).padding.bottom
                    : 0,
                curve: Curves.easeOut,
                decoration: BoxDecoration(
                  // color: Colors.red,
                  boxShadow: [
                    BoxShadow(
                      offset: Offset(0, -4),
                      blurRadius: 30,
                      spreadRadius: 0,
                      color: AppColors.shadowGrey,
                    )
                  ],
                ),
                child: Wrap(
                  children: [
                    BottomNavigationBar(
                      currentIndex: _locationToIndex(location),
                      onTap: (index) {
                        HapticFeedback.lightImpact();

                        int prevLocation = _locationToIndex(location);

                        switch (index) {
                          case 0:
                            if (prevLocation == 0) {
                              context
                                  .read<HomeBloc>()
                                  .scrollController
                                  ?.animateTo(0.0,
                                      duration: Duration(milliseconds: 300),
                                      curve: Curves.easeIn);
                              break;
                            }
                            context.go(RouteNames.home);
                            break;
                          case 1:
                            if (prevLocation == 1) {
                              context
                                  .read<CategoriesBloc>()
                                  .scrollController
                                  ?.animateTo(0.0,
                                      duration: Duration(milliseconds: 300),
                                      curve: Curves.easeIn);
                              break;
                            }
                            pushNewRoute(
                                context, prevLocation, RouteNames.categories);
                            break;
                          case 2:
                            pushNewRoute(
                                context, prevLocation, RouteNames.orders);
                            break;
                          case 3:
                            pushNewRoute(
                                context, prevLocation, RouteNames.cart);
                            break;
                        }
                      },
                      type: BottomNavigationBarType.fixed,
                      backgroundColor: AppColors.surface,
                      elevation: 0, // Elevation from Container's boxShadow
                      selectedItemColor: AppColors.primary,
                      unselectedItemColor: Colors.grey,
                      selectedLabelStyle: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.w500),
                      unselectedLabelStyle: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.w400),
                      items: [
                        BottomNavigationBarItem(
                          icon: BNIcon(path: 'assets/icons/home_icon.png'),
                          activeIcon: BNIcon(
                              isActive: true,
                              path: 'assets/icons/home_icon.png'),
                          label: 'Home',
                        ),
                        BottomNavigationBarItem(
                          icon: BNIcon(path: 'assets/icons/categorie_icon.png'),
                          activeIcon: BNIcon(
                              isActive: true,
                              path: 'assets/icons/categorie_icon.png'),
                          label: 'Categories',
                        ),
                        BottomNavigationBarItem(
                          icon: BNIcon(path: 'assets/icons/order_icon.png'),
                          activeIcon: BNIcon(
                              isActive: true,
                              path: 'assets/icons/order_icon.png'),
                          label: 'Order Again',
                        ),
                        BottomNavigationBarItem(
                          icon: Stack(
                            children: [
                              BNIcon(path: 'assets/icons/my_bag_icon.png'),
                              Positioned(
                                right: MediaQuery.of(context).size.width * 0.08,
                                top: 5,
                                child: BlocBuilder<CartBloc, CartState>(
                                  builder: (context, state) {
                                    int totalCart = state.cart.totalItems;
                                    return Visibility(
                                      visible: totalCart > 0,
                                      child: CircleAvatar(
                                        radius: 10,
                                        backgroundColor: Color(0xFFEF505F),
                                        child: FittedBox(
                                          child: CustomText(
                                            totalCart.toString(),
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontWeight: FontWeight.w800,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                          activeIcon: BNIcon(
                              isActive: true,
                              path: 'assets/icons/my_bag_icon.png'),
                          label: 'My Bag',
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }),
      ),
    );
  }
}

class BNIcon extends StatelessWidget {
  const BNIcon({super.key, this.isActive = false, required this.path});
  final bool isActive;
  final String path;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10, bottom: 8),
      child: Center(
        child: Image.asset(
          path,
          width: 24,
          color: isActive ? AppColors.primary : AppColors.textGrey,
        ),
      ),
    );
  }
}

class CartBNButton extends StatelessWidget {
  const CartBNButton({super.key, required this.isActive});

  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        final itemCount = state.cart.totalItems;

        return Stack(
          clipBehavior: Clip.none,
          children: [
            Icon(
              isActive ? Icons.shopping_cart : Icons.shopping_cart_outlined,
            ),
            if (itemCount > 0)
              Positioned(
                top: -5,
                right: -5,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    itemCount > 9 ? '9+' : itemCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
