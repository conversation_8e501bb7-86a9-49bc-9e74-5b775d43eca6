import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/features/home/<USER>/home%20bloc/home_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/features/home/<USER>/widgets/animated_search_placeholder.dart';
import 'package:rozana/features/home/<USER>/widgets/running_border_container.dart';
import 'package:rozana/features/home/<USER>/widgets/header_widget.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/features/location/presentation/widgets/location_state_handler.dart';
import 'package:rozana/features/products/presentation/widgets/product_section.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/helpers.dart';
import '../../../../data/services/data_loading_manager.dart';
import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_text.dart';
import '../../../../widgets/viewall_category_title.dart';
import '../../../categories/presentation/widgets/category_skeleton_loader.dart';
import '../../../categories/presentation/widgets/categoy_card.dart';

import '../widgets/custom_search_appbar.dart';
import '../widgets/glossy_container.dart';
import '../widgets/section_banner.dart';
import '../widgets/section_categories.dart';
import '../widgets/section_most_bought.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  bool _wasAuthenticated = false;
  // String? _lastAddressId;
  ScrollController categoryScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Initialize authentication state
    final appState = context.read<AppBloc>().state;
    _wasAuthenticated = appState.maybeMap(
      loaded: (loaded) => loaded.isAuthenticated,
      orElse: () => false,
    );
  }

  @override
  void dispose() {
    categoryScrollController.dispose();
    super.dispose();
  }

  final List<Map<String, String>> categoryImages = [
    {'my deals': 'assets/images/my_deals.png'},
    {'grocery': 'assets/images/grocery.png'},
    {'fashion': 'assets/images/fashion.png'},
    {'footwears': 'assets/images/footwears.png'},
  ];

  final List<Color> appBarColors = [
    AppColors.primary,
    const Color.fromARGB(255, 1, 86, 78),
    const Color.fromARGB(255, 22, 112, 25),
    const Color.fromARGB(255, 135, 17, 8),
  ];

  final List<Color> appBarLightColors = [
    Color(0xFF4552CD),
    const Color.fromARGB(255, 92, 242, 227),
    const Color.fromARGB(255, 147, 234, 150),
    const Color.fromARGB(255, 236, 134, 127),
  ];

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to AppBloc changes (authentication)
        BlocListener<AppBloc, AppState>(
          listener: (context, state) {
            state.maybeMap(
              loaded: (loaded) {
                // Check if user just became authenticated
                if (loaded.isAuthenticated && !_wasAuthenticated) {
                  // User just logged in, reload location to show default address
                  context.read<LocationBloc>().reloadDefaultAddress();
                }
                _wasAuthenticated = loaded.isAuthenticated;
              },
              orElse: () {},
            );
          },
        ),
        // Listen to LocationBloc changes (address selection)
        // BlocListener<LocationBloc, LocationState>(
        //   listener: (context, state) {
        //     state.maybeWhen(
        //       loaded: (address) {
        //         // Check if the address has actually changed
        //         if (_lastAddressId != address.id) {
        //           _lastAddressId = address.id;
        //           // Refresh home data when address changes

        //           context.read<HomeBloc>().add(const HomeEvent.loadHomeData());
        //         }
        //       },
        //       orElse: () {},
        //     );
        //   },
        // ),
      ],
      child: Scaffold(
          backgroundColor: AppColors.surface,
          extendBodyBehindAppBar: true,
          // appBar: AppBar(
          //   backgroundColor: Colors.transparent,
          //   toolbarHeight: 120,
          //   titleSpacing: 0,
          //   title: CustomSliverAppBarContent(),
          // ),
          body: Stack(
            children: [
              BlocBuilder<HomeBloc, HomeState>(
                buildWhen: (previous, current) =>
                    (previous.isScrolled != current.isScrolled) ||
                    (previous.selectedIndex != current.selectedIndex),
                builder: (context, state) {
                  // List<CategoryEntity>? fetchedCategories = state.mapOrNull(
                  //     loaded: (value) => value.categorySections);

                  // List<CategoryEntity>? categories = [
                  //   // CategoryEntity(
                  //   //     id: 'All',
                  //   //     name: 'All',
                  //   //     collectionId: 'All',
                  //   //     imageUrl: 'assets/images/shopping-cart.png'
                  //   //     // 'assets/images/store.png'
                  //   //     ),
                  //   ...fetchedCategories ?? []
                  // ];
                  int selectedIndex = state.selectedIndex;
                  return Column(
                    children: [
                      Container(
                        height: 700,
                        decoration: BoxDecoration(
                          color: state.isScrolled
                              ? appBarColors[selectedIndex]
                              : null,
                          gradient: !state.isScrolled
                              ? RadialGradient(
                                  colors: [
                                    // Color(0xFF4552CD),
                                    appBarLightColors[selectedIndex],
                                    // Color(0xFF0C134F),
                                    appBarColors[selectedIndex]
                                  ],
                                  center: Alignment.topCenter,
                                  radius: 1,
                                )
                              : null,
                        ),
                      ),
                      Expanded(child: ColoredBox(color: AppColors.white))
                    ],
                  );
                },
              ),
              SafeArea(
                child: RefreshIndicator(
                  onRefresh: () async {
                    context
                        .read<HomeBloc>()
                        .add(const HomeEvent.loadHomeData());
                  },
                  edgeOffset: 250,
                  child: CustomScrollView(
                    controller: context.read<HomeBloc>().scrollController,
                    slivers: [
                      // Top app bar with categories
                      SliverAppBar(
                        titleSpacing: 0,
                        collapsedHeight: 70,
                        toolbarHeight: 70,
                        expandedHeight: 70,
                        floating: false,
                        pinned: false,
                        stretch: false,
                        snap: false,
                        backgroundColor: Colors.transparent,
                        title: CustomSliverAppBarContent(),
                      ),

                      BlocConsumer<LocationBloc, LocationState>(
                        buildWhen: (previous, current) {
                          return current.maybeMap(
                              loading: (value) => false, orElse: () => true);
                        },
                        listener: (context, state) {
                          state.maybeMap(
                              loaded: (_) async {
                                context
                                    .read<HomeBloc>()
                                    .add(const HomeEvent.loadHomeData());
                                context.read<HomeBloc>().add(
                                    HomeEvent.switchCategory(
                                        CategoryEntity(
                                            id: 'My Deals',
                                            name: 'My Deals',
                                            collectionId: 'My Deals'),
                                        0));
                                // Future.delayed(Duration(milliseconds: 300), () {
                                //   WidgetsBinding.instance
                                //       .scheduleFrameCallback((_) {
                                //     categoryScrollController.animateTo(0.0,
                                //         duration: Duration(milliseconds: 300),
                                //         curve: Curves.easeIn);
                                //   });
                                // });
                              },
                              orElse: () {});
                        },
                        builder: (context, state) {
                          return state.maybeWhen(
                            notServiceable: (_) =>
                                SliverToBoxAdapter(child: SizedBox()),
                            orElse: () => BlocBuilder<HomeBloc, HomeState>(
                              buildWhen: (previous, current) =>
                                  (previous.isScrolled != current.isScrolled) ||
                                  (previous.selectedIndex !=
                                      current.selectedIndex),
                              builder: (context, state) {
                                int selectedIndex = state.selectedIndex;
                                return SliverPersistentHeader(
                                  pinned:
                                      true, // This makes the header stick to the top
                                  delegate: _SliverAppBarDelegate(
                                    minHeight:
                                        (MediaQuery.of(context).size.height *
                                            0.185),
                                    // Height of the blue container when pinned
                                    maxHeight:
                                        (MediaQuery.of(context).size.height *
                                            0.185),
                                    // Height of the blue container when fully expanded
                                    child: ColoredBox(
                                      color: state.isScrolled
                                          ? appBarColors[selectedIndex]
                                          : Colors.transparent,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          // SEARCH BAR
                                          GestureDetector(
                                            onTap: () {
                                              context.push(
                                                  '${RouteNames.search}?initialQuery=');
                                            },
                                            child: Container(
                                              height: 48,
                                              margin: EdgeInsets.symmetric(
                                                  vertical: 10,
                                                  horizontal: AppDimensions
                                                      .screenHzPadding),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 14),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Row(
                                                children: [
                                                  Image.asset(
                                                    'assets/icons/search_icon.png',
                                                    width: 20,
                                                    height: 20,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Row(
                                                    children: [
                                                      CustomText(
                                                        'Search for ',
                                                        color: AppColors.textGrey,
                                                        fontSize: 14,
                                                        maxLines: 1,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        overflow:
                                                            TextOverflow.ellipsis,
                                                        textHeight: 0.9,
                                                     ),
                                                      AnimatedSearchPlaceholder(),
                                                    ],
                                                  )
                                                  
                                                  // Image.asset(
                                                  //   'assets/icons/mic_icon.png',
                                                  //   width: 14,
                                                  //   height: 14,
                                                  // ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Container(
                                            color: state.isScrolled
                                                ? appBarColors[selectedIndex]
                                                : Colors.transparent,
                                            child: BlocBuilder<HomeBloc,
                                                HomeState>(
                                              buildWhen: (previous, current) {
                                                List<CategoryEntity>
                                                    prevCategories =
                                                    previous.mapOrNull(
                                                            loaded: (value) => value
                                                                .categorySections) ??
                                                        [];

                                                List<CategoryEntity>
                                                    currCategories =
                                                    current.mapOrNull(
                                                            loaded: (value) => value
                                                                .categorySections) ??
                                                        [];

                                                return (prevCategories !=
                                                        currCategories) ||
                                                    (previous
                                                            .selectedCategory !=
                                                        current
                                                            .selectedCategory);
                                              },
                                              builder: (context, state) {
                                                if (state is HomeInitial ||
                                                    (state is HomeLoaded &&
                                                        state.categorySections ==
                                                            null)) {
                                                  return Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: AppDimensions
                                                            .screenHzPadding),
                                                    child: SizedBox(
                                                      height:
                                                          MediaQuery.of(context)
                                                                  .size
                                                                  .height *
                                                              0.1,
                                                      child: Row(
                                                        children: [
                                                          Expanded(
                                                              child: Column(
                                                            children: [
                                                              Expanded(
                                                                  child:
                                                                      Padding(
                                                                padding: const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        5),
                                                                child:
                                                                    ShimmerBox(),
                                                              )),
                                                              SizedBox(
                                                                  height: 4),
                                                              ShimmerText(
                                                                  height: 12)
                                                            ],
                                                          )),
                                                          SizedBox(width: 20),
                                                          Expanded(
                                                              child: Column(
                                                            children: [
                                                              Expanded(
                                                                  child:
                                                                      Padding(
                                                                padding: const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        5),
                                                                child:
                                                                    ShimmerBox(),
                                                              )),
                                                              SizedBox(
                                                                  height: 4),
                                                              ShimmerText(
                                                                  height: 12)
                                                            ],
                                                          )),
                                                          SizedBox(width: 20),
                                                          Expanded(
                                                              child: Column(
                                                            children: [
                                                              Expanded(
                                                                  child:
                                                                      Padding(
                                                                padding: const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        5),
                                                                child:
                                                                    ShimmerBox(),
                                                              )),
                                                              SizedBox(
                                                                  height: 4),
                                                              ShimmerText(
                                                                  height: 12)
                                                            ],
                                                          )),
                                                          SizedBox(width: 20),
                                                          Expanded(
                                                              child: Column(
                                                            children: [
                                                              Expanded(
                                                                  child:
                                                                      Padding(
                                                                padding: const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        5),
                                                                child:
                                                                    ShimmerBox(),
                                                              )),
                                                              SizedBox(
                                                                  height: 4),
                                                              ShimmerText(
                                                                  height: 12)
                                                            ],
                                                          )),
                                                        ],
                                                      ),
                                                    ),
                                                  );
                                                  // Align(
                                                  //   alignment:
                                                  //       Alignment.bottomCenter,
                                                  //   child: SkeletonLoaderFactory
                                                  //       .createHorizontalCategoryListSkeleton(
                                                  //     itemCount: 4,
                                                  //     height: 80,
                                                  //   ),
                                                  // );
                                                }

                                                List<CategoryEntity>?
                                                    fetchedCategories =
                                                    state.mapOrNull(
                                                        loaded: (value) => value
                                                            .categorySections);

                                                List<CategoryEntity>?
                                                    categories = [
                                                  // CategoryEntity(
                                                  //     id: 'All',
                                                  //     name: 'All',
                                                  //     collectionId: 'All',
                                                  //     imageUrl:
                                                  //         'assets/images/food.png'
                                                  //     // 'assets/images/store.png'
                                                  //     ),
                                                  ...fetchedCategories ?? []
                                                ];

                                                return Column(
                                                  children: [
                                                    SizedBox(
                                                        // height: 89.4,
                                                        child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: List.generate(
                                                          categories.length,
                                                          (index) {
                                                        CategoryEntity?
                                                            category =
                                                            categories[index];
                                                        bool isSelected = (category
                                                                    .id ==
                                                                state
                                                                    .selectedCategory
                                                                    .id) ||
                                                            (category.name ==
                                                                state
                                                                    .selectedCategory
                                                                    .name);
                                                        return Expanded(
                                                          child: Center(
                                                            child:
                                                                AppBarCategoryBox(
                                                              imagePath:
                                                                  categoryImages
                                                                      .firstWhere(
                                                                        (item) => category
                                                                            .name
                                                                            .toLowerCase()
                                                                            .contains(item.keys.first),
                                                                        orElse:
                                                                            () =>
                                                                                {
                                                                          '': (category.imageUrl ??
                                                                              '')
                                                                        },
                                                                      )
                                                                      .values
                                                                      .first,
                                                              // categoryImages[
                                                              //     index],
                                                              isSelected:
                                                                  isSelected,
                                                              categoryName:
                                                                  TextFormatter
                                                                      .getFormattedCategoryText(
                                                                category.name,
                                                              ),
                                                              onTap: () {
                                                                HapticFeedback
                                                                    .lightImpact();
                                                                context
                                                                    .read<
                                                                        HomeBloc>()
                                                                    .scrollController
                                                                    ?.animateTo(
                                                                        0.0,
                                                                        duration: Duration(
                                                                            milliseconds:
                                                                                500),
                                                                        curve: Curves
                                                                            .easeIn);
                                                                context
                                                                    .read<
                                                                        HomeBloc>()
                                                                    .add(HomeEvent.switchCategory(
                                                                        category,
                                                                        index));
                                                              },
                                                            ),
                                                          ),
                                                        );
                                                      }),
                                                    )
                                                        // ListView.separated(
                                                        //   controller:
                                                        //       categoryScrollController,
                                                        //   scrollDirection:
                                                        //       Axis.horizontal,
                                                        //   shrinkWrap: true,
                                                        //   primary: false,
                                                        //   padding:
                                                        //       EdgeInsets.symmetric(
                                                        //           horizontal: 6),
                                                        //   itemCount:
                                                        //       categories.length,
                                                        //   itemBuilder:
                                                        //       (ctx, index) {
                                                        //     CategoryEntity?
                                                        //         category =
                                                        //         categories[index];

                                                        //     bool isSelected = category
                                                        //             .id ==
                                                        //         state
                                                        //             .selectedCategory
                                                        //             .id;

                                                        //     return AppBarCategoryCard(
                                                        //       key: HomeBloc
                                                        //               .categoryKeys?[
                                                        //           index],
                                                        //       fontSize: 11,
                                                        //       category: category,
                                                        //       isSelected:
                                                        //           isSelected,
                                                        //       index: index,
                                                        //       imageColor:
                                                        //           AppColors.white,
                                                        //     );
                                                        //   },
                                                        //   separatorBuilder:
                                                        //       (context, index) =>
                                                        //           SizedBox(
                                                        //               width: 3),
                                                        // ),
                                                        ),
                                                  ],
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                      ),

                      BlocBuilder<LocationBloc, LocationState>(
                        builder: (context, state) {
                          bool isNotServicable = state.mapOrNull(
                                notServiceable: (value) => true,
                              ) ??
                              false;
                          return SliverToBoxAdapter(
                            child: !isNotServicable
                                ? Column(
                                    children: [
                                      SizedBox(height: 10),
                                      BlocBuilder<HomeBloc, HomeState>(
                                        buildWhen: (previous, current) {
                                          if (previous is! HomeLoaded) {
                                            return true;
                                          }
                                          if (current is HomeLoaded) {
                                            return previous.banners !=
                                                current.banners;
                                          }
                                          return false; // Don’t rebuild for other transitions
                                        },
                                        builder: (context, state) {
                                          CategoryEntity? selectedCategory =
                                              state.selectedCategory;
                                          List<BannerEntity>? banners =
                                              state.mapOrNull(
                                                  loaded: (value) =>
                                                      value.banners);
                                          return (banners?.isNotEmpty ??
                                                      false) &&
                                                  ((selectedCategory
                                                              .isAvailable ??
                                                          false) ||
                                                      selectedCategory.name ==
                                                          'My Deals')
                                              ? BannerSection(
                                                  banners: banners,
                                                  topPadding: 10,
                                                  height: 200,
                                                  showIndicator: false,
                                                  parentCategory:
                                                      selectedCategory,
                                                )
                                              : SizedBox();
                                        },
                                      ),
                                      BlocBuilder<HomeBloc, HomeState>(
                                        builder: (context, state) {
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: AppDimensions
                                                    .screenHzPadding,
                                                vertical: 20),
                                            child: RunningBorderContainer(
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,

                                              height: 60,
                                              containerColor: Colors.white
                                                  .withValues(alpha: 0.1),
                                              borderColor: Colors.white
                                                  .withValues(alpha: 0.4),
                                              runningColor: appBarColors[
                                                  state.selectedIndex],
                                              flashColor: Colors.white
                                                  .withValues(alpha: 0.6),
                                              runningLength:
                                                  0.7, // Percentage of the border length
                                              animationDuration:
                                                  Duration(milliseconds: 1800),
                                              flashDuration:
                                                  Duration(milliseconds: 1200),
                                              cyclesBeforeFlash: 2,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 10,
                                                ),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Row(
                                                        children: [
                                                          Image.asset(
                                                            'assets/images/transport.png',
                                                            width: 26,
                                                            height: 26,
                                                            color: Colors.white,
                                                          ),
                                                          SizedBox(width: 10),
                                                          Flexible(
                                                            child: CustomText(
                                                              'Free Delivery',
                                                              color: AppColors
                                                                  .white,
                                                              maxLines: 2,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    VerticalDivider(
                                                      color: Colors.white
                                                          .withValues(
                                                              alpha: 0.5),
                                                      indent: 10,
                                                      endIndent: 10,
                                                      thickness: 1,
                                                      width: 20,
                                                    ),
                                                    Expanded(
                                                      child: Row(
                                                        children: [
                                                          Image.asset(
                                                            'assets/images/money.png',
                                                            width: 26,
                                                            height: 26,
                                                            color: Colors.white,
                                                          ),
                                                          SizedBox(width: 10),
                                                          Flexible(
                                                            child: CustomText(
                                                              'Cash on Delivery',
                                                              color: AppColors
                                                                  .white,
                                                              maxLines: 2,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    VerticalDivider(
                                                      color: Colors.white
                                                          .withValues(
                                                              alpha: 0.5),
                                                      indent: 10,
                                                      endIndent: 10,
                                                      width: 20,
                                                      thickness: 1,
                                                    ),
                                                    Expanded(
                                                      child: Row(
                                                        children: [
                                                          Image.asset(
                                                            'assets/images/price-tag.png',
                                                            width: 26,
                                                            height: 26,
                                                            color: Colors.white,
                                                          ),
                                                          SizedBox(width: 10),
                                                          Flexible(
                                                            child: CustomText(
                                                              'Lowest price',
                                                              color: AppColors
                                                                  .white,
                                                              maxLines: 2,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  )
                                : SizedBox(),
                          );
                        },
                      ),

                      // Main content list
                      SliverList(
                        delegate: SliverChildListDelegate([
                          BlocBuilder<HomeBloc, HomeState>(
                            buildWhen: (previous, current) =>
                                (previous is! HomeError) ||
                                (current is! HomeError),
                            builder: (context, state) {
                              return Container(
                                color: AppColors.white,
                                child: state.maybeMap(
                                  error: (value) =>
                                      _buildErrorState(value.message, context),
                                  orElse: () => LocationStateHandler(
                                    builderHeight:
                                        MediaQuery.of(context).size.height -
                                            100,
                                    child: HomeBody(),
                                  ),
                                ),
                              );
                            },
                          ),
                        ]),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          )),
    );
  }
}

class HomeBody extends StatelessWidget {
  const HomeBody({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) =>
          previous.selectedCategory != current.selectedCategory,
      builder: (context, state) {
        CategoryEntity? category = state.selectedCategory;
        // final screenWidth = MediaQuery.of(context).size.width;
        return Column(
          children: [
            (category.name.toLowerCase() == 'my deals')
                ? HomeAllData()
                : HomeCategoriesSection(
                    preloadData: true,
                    showTitleBar: false,
                    parentCategory: category,
                    level: 'sub_category',
                    // onSeeAllTap: () async {
                    //   context.push(RouteNames.products, extra: {
                    //     'category': category,
                    //   });
                    //   // Log category view event to AppsFlyer
                    //   // await AppsFlyerEvents.categoryView(
                    //   //   category?.id ?? '',
                    //   //   category?.name ?? '',
                    //   // );
                    // },
                  ),
          ],
        );
      },
    );
  }
}

class HomeAllData extends StatelessWidget {
  const HomeAllData({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // SizedBox(height: 24),
        //Categories section
        CategoriesSection(),
        // TopCategoriesSection(),
        // SizedBox(height: 24),

        // Section99store(),
        // SizedBox(height: 24),
        // MegaMonsoonSection(),
        // SizedBox(height: 24),
        // Previiously Bought
        // PreviouslyBoughtSection(),
        // SizedBox(height: 12),
        // Most popular
        // MostPopularSection(),

        // Banners section
        // BlocBuilder<HomeBloc, HomeState>(
        //   buildWhen: (previous, current) {
        //     if (previous is! HomeLoaded) {
        //       return true;
        //     }
        //     if (current is HomeLoaded) {
        //       return previous.banners != current.banners;
        //     }
        //     return false; // Don’t rebuild for other transitions
        //   },
        //   builder: (context, state) {
        //     List<BannerEntity>? banners =
        //         state.mapOrNull(loaded: (value) => value.banners);
        //     return BannerSection(banners: banners);
        //   },
        // ),
        SizedBox(height: 30),
        // BudgetBazaarSection(),

        // Most Bought
        MostBoughtSection(),
        SizedBox(height: 12),
        // Combo Deal section
        // ComboDealSection(
        //   productCombos: [
        //     ProductComboModel(
        //         products: [ProductModel(), ProductModel(), ProductModel()]),
        //     ProductComboModel(products: [ProductModel(), ProductModel()]),
        //     ProductComboModel(
        //         products: [ProductModel(), ProductModel(), ProductModel()])
        //   ],
        // ),

        //Deals section
        // DealsSection(),

        //Shop by store section
        // StoreSection(),

        //SuperPack section
        // SuperPackSection(),

        // Rozana logo
        // RozanaLogoImage(
        //   padding: EdgeInsets.only(top: 30, bottom: 40),
        //   width: MediaQuery.of(context).size.width * 0.8,
        // ),
        SizedBox(height: 150),
      ],
    );
  }
}

Widget _buildErrorState(String message, BuildContext context) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.error_outline, color: Colors.red, size: 50),
        const SizedBox(height: 10),
        Text(message, textAlign: TextAlign.center),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            context.read<HomeBloc>().add(const HomeEvent.loadHomeData());
          },
          child: const Text('Retry'),
        ),
      ],
    ),
  );
}

class CurvedBottomPainter extends CustomPainter {
  final Color lightColor;
  final Color darkColor;

  CurvedBottomPainter({
    super.repaint,
    this.lightColor = AppColors.primary,
    this.darkColor = AppColors.primaryDark,
  });
  @override
  void paint(Canvas canvas, Size size) {
    final Rect rect = Rect.fromLTWH(0, 0, size.width, size.height);

    final Gradient gradient = RadialGradient(
      center: Alignment.center,
      radius: 0.8,
      colors: [
        lightColor, // light center
        darkColor
      ],
      stops: [0.01, 1.0],
    );

    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(0, size.height - 40)
      ..quadraticBezierTo(
        size.width / 2,
        size.height + 40,
        size.width,
        size.height - 40,
      )
      ..lineTo(size.width, 0)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class HomeCategoriesSection extends StatefulWidget {
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;
  final String? level;

  const HomeCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
    this.level,
  });

  @override
  State<HomeCategoriesSection> createState() => _HomeCategoriesSectionState();
}

class _HomeCategoriesSectionState extends State<HomeCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  @override
  void didUpdateWidget(covariant HomeCategoriesSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if the parentCategory has changed
    if (widget.parentCategory != oldWidget.parentCategory ||
        widget.level != oldWidget.level) {
      // Also check if level changed if it impacts data loading
      LogMessage.l(
          "Parent category changed from ${oldWidget.parentCategory?.name} to ${widget.parentCategory?.name}");
      _loadCategories(); // Reload categories when parentCategory changes
    }
  }

  Future<void> _loadCategories() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final categories = widget.parentCategory == null
          ? await _dataManager.loadCategories(pageSize: 20)
          : await _dataManager.loadSubCategories(
              category: widget.parentCategory,
              pageSize: 20,
              level: widget.level);

      setState(() {
        _subCategories.clear();
        _subCategories.addAll(categories);
        _isLoading = false;
      });
      if (_subCategories.isNotEmpty && widget.onSubcategorySelected != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onSubcategorySelected!(_subCategories.first);
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: (widget.parentCategory?.isAvailable ?? false) || _isLoading
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _isLoading
                    ? Center(
                        child: SizedBox(
                          height: 300,
                        ),
                      )
                    : HomeCategorySectionWidget(
                        parentCategory: widget.parentCategory,
                        subCategories: _subCategories,
                      ),
                if (widget.parentCategory != null) ...[
                  HeaderWidget(title: "Top Products"),
                  const SizedBox(height: 12),
                  ProductsSection(
                    title: widget.parentCategory?.name ?? 'All Products',
                    showSeeAll: false,
                    category: widget.parentCategory,
                    useGridView: true,
                    shrinkWrap: true,
                    primary: true,
                    physics: NeverScrollableScrollPhysics(),
                    bottomPadding: 100,
                    useCollectionId: true,

                    // gridCrossAxisCount: screenWidth > 600 ? 3 : 2,
                    gridChildAspectRatio: 0.7,
                    // scrollController: _scrollController,
                    onSeeAllTap: () async {
                      // context.push(
                      //   RouteNames.products,
                      //   extra: {
                      //     'category': _selectedSubCategory,
                      //     'viewAll': true,
                      //   },
                      // );
                      // Log category view event to AppsFlyer
                    },
                    onProductTap: (product) {
                      context.push(
                        RouteNames.productDetail,
                        extra: {
                          'product': product,
                        },
                      );
                    },
                  ),
                ]
              ],
            )
          : Padding(
              padding: const EdgeInsets.only(top: 100),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Image.asset('assets/images/curve_background.png'),
                  ),
                  SizedBox(
                    height: 150,
                    width: MediaQuery.of(context).size.width,
                    child: Image.asset('assets/images/coming-soon.png'),
                  ),
                ],
              ),
            ),
    );
  }
}

class HomeSubCategoriesSection extends StatefulWidget {
  final CategoryEntity? mainCategory;
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;
  final String? level;

  const HomeSubCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
    this.level,
    this.mainCategory,
  });

  @override
  State<HomeSubCategoriesSection> createState() =>
      _HomeSubCategoriesSectionState();
}

class _HomeSubCategoriesSectionState extends State<HomeSubCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  Future<void> _loadCategories() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final categories = widget.parentCategory == null
          ? await _dataManager.loadCategories(pageSize: 20)
          : await _dataManager.loadSubCategories(
              category: widget.parentCategory,
              pageSize: 20,
              level: widget.level);

      setState(() {
        _subCategories.clear();
        _subCategories.addAll(categories);
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitleBar)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 8),
            child: ViewAllCategoryTitle(
              title: widget.parentCategory?.name ?? '',
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onSeeAllTap?.call();
              },
              showViewAll: false,
            ),
          ),
        const SizedBox(height: 8),
        _isLoading
            ? Center(
                child: Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: CategorySkeletonLoader(
                    useGridView: widget.useGridView,
                    showAsRow: widget.showAsRow,
                    gridCrossAxisCount: widget.gridCrossAxisCount,
                    gridChildAspectRatio: 1,
                    itemHeight: 70,
                  ),
                ),
              )
            : GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.screenHzPadding),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: widget.gridCrossAxisCount,
                  childAspectRatio: widget.gridChildAspectRatio,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 12,
                ),
                itemCount: _subCategories.length,
                itemBuilder: (context, index) {
                  CategoryEntity subCategory = _subCategories[index];
                  return CategoryCard(
                    onTap: () async {
                      HapticFeedback.lightImpact();

                      Map<String, dynamic> extras = {};
                      if (widget.parentCategory != null) {
                        extras['category'] = widget.parentCategory;
                        extras['sub_category'] = subCategory;
                        extras['parent-category'] = widget.mainCategory;
                      }
                      context.push(RouteNames.products, extra: extras);
                    },
                    category: widget.parentCategory,
                    subCategory: subCategory,
                    radius: 10,
                    fontSize: 10,
                  );
                },
              ),
      ],
    );
  }
}

class HomeCategorySectionWidget extends StatelessWidget {
  const HomeCategorySectionWidget({
    super.key,
    this.parentCategory,
    this.subCategories = const [],
  });
  final CategoryEntity? parentCategory;
  final List<CategoryEntity> subCategories;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 20,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Container(
            // decoration: BoxDecoration(
            //   color: ColorUtils.generateConsistentColor(parentCategory?.id ?? '',
            //       baseOpacity: 0.2),
            //   borderRadius: BorderRadius.circular(8),
            // ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Display parent category name as title (like CategoriesSection)
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 16, right: 8, bottom: 15),
                    child: HeaderWidget(title: parentCategory?.name ?? ''),
                  ),
                  // Display subcategories in grid format (like CategoriesSection)
                  GridView.builder(
                    shrinkWrap: true,
                    primary: false,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.screenHzPadding),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 4,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 10,
                      childAspectRatio: 0.8,
                    ),
                    itemCount: subCategories.length,
                    itemBuilder: (ctx, index) {
                      CategoryEntity subCategory = subCategories[index];
                      return CategoryCard(
                        fontSize: 8,
                        subCategory: subCategory,
                        category: CategoryEntity(
                            id: '',
                            name: '',
                            collectionId: subCategory.parentID ?? ''),
                      );
                    },
                  ),
                  // Bottom padding
                ],
              ),
            ),
          ),
        ),
        SizedBox(height: 20),
      ],
    );
  }
}

// Custom SliverPersistentHeaderDelegate
// This is boilerplate code needed for SliverPersistentHeader
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
