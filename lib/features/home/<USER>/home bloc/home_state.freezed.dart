// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HomeState {
  bool get isScrolled;
  bool get showBottomNavBar;
  CategoryEntity get selectedCategory;
  int get selectedIndex;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HomeStateCopyWith<HomeState> get copyWith =>
      _$HomeStateCopyWithImpl<HomeState>(this as HomeState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HomeState &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled) &&
            (identical(other.showBottomNavBar, showBottomNavBar) ||
                other.showBottomNavBar == showBottomNavBar) &&
            (identical(other.selectedCategory, selectedCategory) ||
                other.selectedCategory == selectedCategory) &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isScrolled, showBottomNavBar,
      selectedCategory, selectedIndex);

  @override
  String toString() {
    return 'HomeState(isScrolled: $isScrolled, showBottomNavBar: $showBottomNavBar, selectedCategory: $selectedCategory, selectedIndex: $selectedIndex)';
  }
}

/// @nodoc
abstract mixin class $HomeStateCopyWith<$Res> {
  factory $HomeStateCopyWith(HomeState value, $Res Function(HomeState) _then) =
      _$HomeStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isScrolled,
      bool showBottomNavBar,
      CategoryEntity selectedCategory,
      int selectedIndex});
}

/// @nodoc
class _$HomeStateCopyWithImpl<$Res> implements $HomeStateCopyWith<$Res> {
  _$HomeStateCopyWithImpl(this._self, this._then);

  final HomeState _self;
  final $Res Function(HomeState) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isScrolled = null,
    Object? showBottomNavBar = null,
    Object? selectedCategory = null,
    Object? selectedIndex = null,
  }) {
    return _then(_self.copyWith(
      isScrolled: null == isScrolled
          ? _self.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
      showBottomNavBar: null == showBottomNavBar
          ? _self.showBottomNavBar
          : showBottomNavBar // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedCategory: null == selectedCategory
          ? _self.selectedCategory
          : selectedCategory // ignore: cast_nullable_to_non_nullable
              as CategoryEntity,
      selectedIndex: null == selectedIndex
          ? _self.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// Adds pattern-matching-related methods to [HomeState].
extension HomeStatePatterns on HomeState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(HomeInitial value)? initial,
    TResult Function(HomeLoaded value)? loaded,
    TResult Function(HomeError value)? error,
    TResult Function(HomeDeepLink value)? deepLink,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case HomeInitial() when initial != null:
        return initial(_that);
      case HomeLoaded() when loaded != null:
        return loaded(_that);
      case HomeError() when error != null:
        return error(_that);
      case HomeDeepLink() when deepLink != null:
        return deepLink(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(HomeInitial value) initial,
    required TResult Function(HomeLoaded value) loaded,
    required TResult Function(HomeError value) error,
    required TResult Function(HomeDeepLink value) deepLink,
  }) {
    final _that = this;
    switch (_that) {
      case HomeInitial():
        return initial(_that);
      case HomeLoaded():
        return loaded(_that);
      case HomeError():
        return error(_that);
      case HomeDeepLink():
        return deepLink(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(HomeInitial value)? initial,
    TResult? Function(HomeLoaded value)? loaded,
    TResult? Function(HomeError value)? error,
    TResult? Function(HomeDeepLink value)? deepLink,
  }) {
    final _that = this;
    switch (_that) {
      case HomeInitial() when initial != null:
        return initial(_that);
      case HomeLoaded() when loaded != null:
        return loaded(_that);
      case HomeError() when error != null:
        return error(_that);
      case HomeDeepLink() when deepLink != null:
        return deepLink(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isScrolled, bool showBottomNavBar,
            CategoryEntity selectedCategory, int selectedIndex)?
        initial,
    TResult Function(
            List<CategoryEntity>? categorySections,
            List<CategoryEntity>? categories,
            List<CategoryEntity>? subCategories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled,
            bool showBottomNavBar,
            CategoryEntity selectedCategory,
            int selectedIndex)?
        loaded,
    TResult Function(String message, bool isScrolled, bool showBottomNavBar,
            CategoryEntity selectedCategory, int selectedIndex)?
        error,
    TResult Function(
            bool isScrolled,
            bool showBottomNavBar,
            CategoryEntity selectedCategory,
            int selectedIndex,
            String route,
            Map<String, dynamic> args)?
        deepLink,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case HomeInitial() when initial != null:
        return initial(_that.isScrolled, _that.showBottomNavBar,
            _that.selectedCategory, _that.selectedIndex);
      case HomeLoaded() when loaded != null:
        return loaded(
            _that.categorySections,
            _that.categories,
            _that.subCategories,
            _that.previouslyBought,
            _that.mostPopular,
            _that.mostBought,
            _that.banners,
            _that.isScrolled,
            _that.showBottomNavBar,
            _that.selectedCategory,
            _that.selectedIndex);
      case HomeError() when error != null:
        return error(_that.message, _that.isScrolled, _that.showBottomNavBar,
            _that.selectedCategory, _that.selectedIndex);
      case HomeDeepLink() when deepLink != null:
        return deepLink(
            _that.isScrolled,
            _that.showBottomNavBar,
            _that.selectedCategory,
            _that.selectedIndex,
            _that.route,
            _that.args);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isScrolled, bool showBottomNavBar,
            CategoryEntity selectedCategory, int selectedIndex)
        initial,
    required TResult Function(
            List<CategoryEntity>? categorySections,
            List<CategoryEntity>? categories,
            List<CategoryEntity>? subCategories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled,
            bool showBottomNavBar,
            CategoryEntity selectedCategory,
            int selectedIndex)
        loaded,
    required TResult Function(
            String message,
            bool isScrolled,
            bool showBottomNavBar,
            CategoryEntity selectedCategory,
            int selectedIndex)
        error,
    required TResult Function(
            bool isScrolled,
            bool showBottomNavBar,
            CategoryEntity selectedCategory,
            int selectedIndex,
            String route,
            Map<String, dynamic> args)
        deepLink,
  }) {
    final _that = this;
    switch (_that) {
      case HomeInitial():
        return initial(_that.isScrolled, _that.showBottomNavBar,
            _that.selectedCategory, _that.selectedIndex);
      case HomeLoaded():
        return loaded(
            _that.categorySections,
            _that.categories,
            _that.subCategories,
            _that.previouslyBought,
            _that.mostPopular,
            _that.mostBought,
            _that.banners,
            _that.isScrolled,
            _that.showBottomNavBar,
            _that.selectedCategory,
            _that.selectedIndex);
      case HomeError():
        return error(_that.message, _that.isScrolled, _that.showBottomNavBar,
            _that.selectedCategory, _that.selectedIndex);
      case HomeDeepLink():
        return deepLink(
            _that.isScrolled,
            _that.showBottomNavBar,
            _that.selectedCategory,
            _that.selectedIndex,
            _that.route,
            _that.args);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isScrolled, bool showBottomNavBar,
            CategoryEntity selectedCategory, int selectedIndex)?
        initial,
    TResult? Function(
            List<CategoryEntity>? categorySections,
            List<CategoryEntity>? categories,
            List<CategoryEntity>? subCategories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners,
            bool isScrolled,
            bool showBottomNavBar,
            CategoryEntity selectedCategory,
            int selectedIndex)?
        loaded,
    TResult? Function(String message, bool isScrolled, bool showBottomNavBar,
            CategoryEntity selectedCategory, int selectedIndex)?
        error,
    TResult? Function(
            bool isScrolled,
            bool showBottomNavBar,
            CategoryEntity selectedCategory,
            int selectedIndex,
            String route,
            Map<String, dynamic> args)?
        deepLink,
  }) {
    final _that = this;
    switch (_that) {
      case HomeInitial() when initial != null:
        return initial(_that.isScrolled, _that.showBottomNavBar,
            _that.selectedCategory, _that.selectedIndex);
      case HomeLoaded() when loaded != null:
        return loaded(
            _that.categorySections,
            _that.categories,
            _that.subCategories,
            _that.previouslyBought,
            _that.mostPopular,
            _that.mostBought,
            _that.banners,
            _that.isScrolled,
            _that.showBottomNavBar,
            _that.selectedCategory,
            _that.selectedIndex);
      case HomeError() when error != null:
        return error(_that.message, _that.isScrolled, _that.showBottomNavBar,
            _that.selectedCategory, _that.selectedIndex);
      case HomeDeepLink() when deepLink != null:
        return deepLink(
            _that.isScrolled,
            _that.showBottomNavBar,
            _that.selectedCategory,
            _that.selectedIndex,
            _that.route,
            _that.args);
      case _:
        return null;
    }
  }
}

/// @nodoc

class HomeInitial implements HomeState {
  const HomeInitial(
      {this.isScrolled = false,
      this.showBottomNavBar = true,
      this.selectedCategory = const CategoryEntity(
          id: 'My Deals', name: 'My Deals', collectionId: 'My Deals'),
      this.selectedIndex = 0});

  @override
  @JsonKey()
  final bool isScrolled;
  @override
  @JsonKey()
  final bool showBottomNavBar;
  @override
  @JsonKey()
  final CategoryEntity selectedCategory;
  @override
  @JsonKey()
  final int selectedIndex;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HomeInitialCopyWith<HomeInitial> get copyWith =>
      _$HomeInitialCopyWithImpl<HomeInitial>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HomeInitial &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled) &&
            (identical(other.showBottomNavBar, showBottomNavBar) ||
                other.showBottomNavBar == showBottomNavBar) &&
            (identical(other.selectedCategory, selectedCategory) ||
                other.selectedCategory == selectedCategory) &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isScrolled, showBottomNavBar,
      selectedCategory, selectedIndex);

  @override
  String toString() {
    return 'HomeState.initial(isScrolled: $isScrolled, showBottomNavBar: $showBottomNavBar, selectedCategory: $selectedCategory, selectedIndex: $selectedIndex)';
  }
}

/// @nodoc
abstract mixin class $HomeInitialCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory $HomeInitialCopyWith(
          HomeInitial value, $Res Function(HomeInitial) _then) =
      _$HomeInitialCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isScrolled,
      bool showBottomNavBar,
      CategoryEntity selectedCategory,
      int selectedIndex});
}

/// @nodoc
class _$HomeInitialCopyWithImpl<$Res> implements $HomeInitialCopyWith<$Res> {
  _$HomeInitialCopyWithImpl(this._self, this._then);

  final HomeInitial _self;
  final $Res Function(HomeInitial) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isScrolled = null,
    Object? showBottomNavBar = null,
    Object? selectedCategory = null,
    Object? selectedIndex = null,
  }) {
    return _then(HomeInitial(
      isScrolled: null == isScrolled
          ? _self.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
      showBottomNavBar: null == showBottomNavBar
          ? _self.showBottomNavBar
          : showBottomNavBar // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedCategory: null == selectedCategory
          ? _self.selectedCategory
          : selectedCategory // ignore: cast_nullable_to_non_nullable
              as CategoryEntity,
      selectedIndex: null == selectedIndex
          ? _self.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class HomeLoaded implements HomeState {
  const HomeLoaded(
      {required final List<CategoryEntity>? categorySections,
      required final List<CategoryEntity>? categories,
      required final List<CategoryEntity>? subCategories,
      required final List<ProductEntity>? previouslyBought,
      required final List<ProductEntity>? mostPopular,
      required final List<ProductEntity>? mostBought,
      required final List<BannerEntity>? banners,
      required this.isScrolled,
      this.showBottomNavBar = true,
      this.selectedCategory = const CategoryEntity(
          id: 'My Deals', name: 'My Deals', collectionId: 'My Deals'),
      this.selectedIndex = 0})
      : _categorySections = categorySections,
        _categories = categories,
        _subCategories = subCategories,
        _previouslyBought = previouslyBought,
        _mostPopular = mostPopular,
        _mostBought = mostBought,
        _banners = banners;

  final List<CategoryEntity>? _categorySections;
  List<CategoryEntity>? get categorySections {
    final value = _categorySections;
    if (value == null) return null;
    if (_categorySections is EqualUnmodifiableListView)
      return _categorySections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<CategoryEntity>? _categories;
  List<CategoryEntity>? get categories {
    final value = _categories;
    if (value == null) return null;
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<CategoryEntity>? _subCategories;
  List<CategoryEntity>? get subCategories {
    final value = _subCategories;
    if (value == null) return null;
    if (_subCategories is EqualUnmodifiableListView) return _subCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _previouslyBought;
  List<ProductEntity>? get previouslyBought {
    final value = _previouslyBought;
    if (value == null) return null;
    if (_previouslyBought is EqualUnmodifiableListView)
      return _previouslyBought;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _mostPopular;
  List<ProductEntity>? get mostPopular {
    final value = _mostPopular;
    if (value == null) return null;
    if (_mostPopular is EqualUnmodifiableListView) return _mostPopular;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _mostBought;
  List<ProductEntity>? get mostBought {
    final value = _mostBought;
    if (value == null) return null;
    if (_mostBought is EqualUnmodifiableListView) return _mostBought;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<BannerEntity>? _banners;
  List<BannerEntity>? get banners {
    final value = _banners;
    if (value == null) return null;
    if (_banners is EqualUnmodifiableListView) return _banners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool isScrolled;
  @override
  @JsonKey()
  final bool showBottomNavBar;
  @override
  @JsonKey()
  final CategoryEntity selectedCategory;
  @override
  @JsonKey()
  final int selectedIndex;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HomeLoadedCopyWith<HomeLoaded> get copyWith =>
      _$HomeLoadedCopyWithImpl<HomeLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HomeLoaded &&
            const DeepCollectionEquality()
                .equals(other._categorySections, _categorySections) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._subCategories, _subCategories) &&
            const DeepCollectionEquality()
                .equals(other._previouslyBought, _previouslyBought) &&
            const DeepCollectionEquality()
                .equals(other._mostPopular, _mostPopular) &&
            const DeepCollectionEquality()
                .equals(other._mostBought, _mostBought) &&
            const DeepCollectionEquality().equals(other._banners, _banners) &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled) &&
            (identical(other.showBottomNavBar, showBottomNavBar) ||
                other.showBottomNavBar == showBottomNavBar) &&
            (identical(other.selectedCategory, selectedCategory) ||
                other.selectedCategory == selectedCategory) &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_categorySections),
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_subCategories),
      const DeepCollectionEquality().hash(_previouslyBought),
      const DeepCollectionEquality().hash(_mostPopular),
      const DeepCollectionEquality().hash(_mostBought),
      const DeepCollectionEquality().hash(_banners),
      isScrolled,
      showBottomNavBar,
      selectedCategory,
      selectedIndex);

  @override
  String toString() {
    return 'HomeState.loaded(categorySections: $categorySections, categories: $categories, subCategories: $subCategories, previouslyBought: $previouslyBought, mostPopular: $mostPopular, mostBought: $mostBought, banners: $banners, isScrolled: $isScrolled, showBottomNavBar: $showBottomNavBar, selectedCategory: $selectedCategory, selectedIndex: $selectedIndex)';
  }
}

/// @nodoc
abstract mixin class $HomeLoadedCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory $HomeLoadedCopyWith(
          HomeLoaded value, $Res Function(HomeLoaded) _then) =
      _$HomeLoadedCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<CategoryEntity>? categorySections,
      List<CategoryEntity>? categories,
      List<CategoryEntity>? subCategories,
      List<ProductEntity>? previouslyBought,
      List<ProductEntity>? mostPopular,
      List<ProductEntity>? mostBought,
      List<BannerEntity>? banners,
      bool isScrolled,
      bool showBottomNavBar,
      CategoryEntity selectedCategory,
      int selectedIndex});
}

/// @nodoc
class _$HomeLoadedCopyWithImpl<$Res> implements $HomeLoadedCopyWith<$Res> {
  _$HomeLoadedCopyWithImpl(this._self, this._then);

  final HomeLoaded _self;
  final $Res Function(HomeLoaded) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? categorySections = freezed,
    Object? categories = freezed,
    Object? subCategories = freezed,
    Object? previouslyBought = freezed,
    Object? mostPopular = freezed,
    Object? mostBought = freezed,
    Object? banners = freezed,
    Object? isScrolled = null,
    Object? showBottomNavBar = null,
    Object? selectedCategory = null,
    Object? selectedIndex = null,
  }) {
    return _then(HomeLoaded(
      categorySections: freezed == categorySections
          ? _self._categorySections
          : categorySections // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      categories: freezed == categories
          ? _self._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      subCategories: freezed == subCategories
          ? _self._subCategories
          : subCategories // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      previouslyBought: freezed == previouslyBought
          ? _self._previouslyBought
          : previouslyBought // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      mostPopular: freezed == mostPopular
          ? _self._mostPopular
          : mostPopular // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      mostBought: freezed == mostBought
          ? _self._mostBought
          : mostBought // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      banners: freezed == banners
          ? _self._banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<BannerEntity>?,
      isScrolled: null == isScrolled
          ? _self.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
      showBottomNavBar: null == showBottomNavBar
          ? _self.showBottomNavBar
          : showBottomNavBar // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedCategory: null == selectedCategory
          ? _self.selectedCategory
          : selectedCategory // ignore: cast_nullable_to_non_nullable
              as CategoryEntity,
      selectedIndex: null == selectedIndex
          ? _self.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class HomeError implements HomeState {
  const HomeError(
      {required this.message,
      required this.isScrolled,
      this.showBottomNavBar = true,
      this.selectedCategory = const CategoryEntity(
          id: 'My Deals', name: 'My Deals', collectionId: 'My Deals'),
      this.selectedIndex = 0});

  final String message;
  @override
  final bool isScrolled;
  @override
  @JsonKey()
  final bool showBottomNavBar;
  @override
  @JsonKey()
  final CategoryEntity selectedCategory;
  @override
  @JsonKey()
  final int selectedIndex;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HomeErrorCopyWith<HomeError> get copyWith =>
      _$HomeErrorCopyWithImpl<HomeError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HomeError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled) &&
            (identical(other.showBottomNavBar, showBottomNavBar) ||
                other.showBottomNavBar == showBottomNavBar) &&
            (identical(other.selectedCategory, selectedCategory) ||
                other.selectedCategory == selectedCategory) &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, isScrolled,
      showBottomNavBar, selectedCategory, selectedIndex);

  @override
  String toString() {
    return 'HomeState.error(message: $message, isScrolled: $isScrolled, showBottomNavBar: $showBottomNavBar, selectedCategory: $selectedCategory, selectedIndex: $selectedIndex)';
  }
}

/// @nodoc
abstract mixin class $HomeErrorCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory $HomeErrorCopyWith(HomeError value, $Res Function(HomeError) _then) =
      _$HomeErrorCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String message,
      bool isScrolled,
      bool showBottomNavBar,
      CategoryEntity selectedCategory,
      int selectedIndex});
}

/// @nodoc
class _$HomeErrorCopyWithImpl<$Res> implements $HomeErrorCopyWith<$Res> {
  _$HomeErrorCopyWithImpl(this._self, this._then);

  final HomeError _self;
  final $Res Function(HomeError) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? isScrolled = null,
    Object? showBottomNavBar = null,
    Object? selectedCategory = null,
    Object? selectedIndex = null,
  }) {
    return _then(HomeError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      isScrolled: null == isScrolled
          ? _self.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
      showBottomNavBar: null == showBottomNavBar
          ? _self.showBottomNavBar
          : showBottomNavBar // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedCategory: null == selectedCategory
          ? _self.selectedCategory
          : selectedCategory // ignore: cast_nullable_to_non_nullable
              as CategoryEntity,
      selectedIndex: null == selectedIndex
          ? _self.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class HomeDeepLink implements HomeState {
  const HomeDeepLink(
      {required this.isScrolled,
      this.showBottomNavBar = true,
      this.selectedCategory = const CategoryEntity(
          id: 'My Deals', name: 'My Deals', collectionId: 'My Deals'),
      this.selectedIndex = 0,
      required this.route,
      final Map<String, dynamic> args = const {}})
      : _args = args;

  @override
  final bool isScrolled;
  @override
  @JsonKey()
  final bool showBottomNavBar;
  @override
  @JsonKey()
  final CategoryEntity selectedCategory;
  @override
  @JsonKey()
  final int selectedIndex;
  final String route;
  final Map<String, dynamic> _args;
  @JsonKey()
  Map<String, dynamic> get args {
    if (_args is EqualUnmodifiableMapView) return _args;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_args);
  }

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HomeDeepLinkCopyWith<HomeDeepLink> get copyWith =>
      _$HomeDeepLinkCopyWithImpl<HomeDeepLink>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HomeDeepLink &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled) &&
            (identical(other.showBottomNavBar, showBottomNavBar) ||
                other.showBottomNavBar == showBottomNavBar) &&
            (identical(other.selectedCategory, selectedCategory) ||
                other.selectedCategory == selectedCategory) &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex) &&
            (identical(other.route, route) || other.route == route) &&
            const DeepCollectionEquality().equals(other._args, _args));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isScrolled,
      showBottomNavBar,
      selectedCategory,
      selectedIndex,
      route,
      const DeepCollectionEquality().hash(_args));

  @override
  String toString() {
    return 'HomeState.deepLink(isScrolled: $isScrolled, showBottomNavBar: $showBottomNavBar, selectedCategory: $selectedCategory, selectedIndex: $selectedIndex, route: $route, args: $args)';
  }
}

/// @nodoc
abstract mixin class $HomeDeepLinkCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory $HomeDeepLinkCopyWith(
          HomeDeepLink value, $Res Function(HomeDeepLink) _then) =
      _$HomeDeepLinkCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isScrolled,
      bool showBottomNavBar,
      CategoryEntity selectedCategory,
      int selectedIndex,
      String route,
      Map<String, dynamic> args});
}

/// @nodoc
class _$HomeDeepLinkCopyWithImpl<$Res> implements $HomeDeepLinkCopyWith<$Res> {
  _$HomeDeepLinkCopyWithImpl(this._self, this._then);

  final HomeDeepLink _self;
  final $Res Function(HomeDeepLink) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isScrolled = null,
    Object? showBottomNavBar = null,
    Object? selectedCategory = null,
    Object? selectedIndex = null,
    Object? route = null,
    Object? args = null,
  }) {
    return _then(HomeDeepLink(
      isScrolled: null == isScrolled
          ? _self.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
      showBottomNavBar: null == showBottomNavBar
          ? _self.showBottomNavBar
          : showBottomNavBar // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedCategory: null == selectedCategory
          ? _self.selectedCategory
          : selectedCategory // ignore: cast_nullable_to_non_nullable
              as CategoryEntity,
      selectedIndex: null == selectedIndex
          ? _self.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
      route: null == route
          ? _self.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      args: null == args
          ? _self._args
          : args // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

// dart format on
