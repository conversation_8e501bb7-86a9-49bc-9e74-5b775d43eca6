// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HomeEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is HomeEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HomeEvent()';
  }
}

/// @nodoc
class $HomeEventCopyWith<$Res> {
  $HomeEventCopyWith(HomeEvent _, $Res Function(HomeEvent) __);
}

/// Adds pattern-matching-related methods to [HomeEvent].
extension HomeEventPatterns on HomeEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitHome value)? init,
    TResult Function(UpdateScroll value)? updateScroll,
    TResult Function(UpdateHomeList value)? updateLoadedList,
    TResult Function(LoadHomeData value)? loadHomeData,
    TResult Function(LoadDeepLink value)? deepLinkFound,
    TResult Function(ScrollDirectionChanged value)? scrollDirectionChanged,
    TResult Function(SwitchCategory value)? switchCategory,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case InitHome() when init != null:
        return init(_that);
      case UpdateScroll() when updateScroll != null:
        return updateScroll(_that);
      case UpdateHomeList() when updateLoadedList != null:
        return updateLoadedList(_that);
      case LoadHomeData() when loadHomeData != null:
        return loadHomeData(_that);
      case LoadDeepLink() when deepLinkFound != null:
        return deepLinkFound(_that);
      case ScrollDirectionChanged() when scrollDirectionChanged != null:
        return scrollDirectionChanged(_that);
      case SwitchCategory() when switchCategory != null:
        return switchCategory(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitHome value) init,
    required TResult Function(UpdateScroll value) updateScroll,
    required TResult Function(UpdateHomeList value) updateLoadedList,
    required TResult Function(LoadHomeData value) loadHomeData,
    required TResult Function(LoadDeepLink value) deepLinkFound,
    required TResult Function(ScrollDirectionChanged value)
        scrollDirectionChanged,
    required TResult Function(SwitchCategory value) switchCategory,
  }) {
    final _that = this;
    switch (_that) {
      case InitHome():
        return init(_that);
      case UpdateScroll():
        return updateScroll(_that);
      case UpdateHomeList():
        return updateLoadedList(_that);
      case LoadHomeData():
        return loadHomeData(_that);
      case LoadDeepLink():
        return deepLinkFound(_that);
      case ScrollDirectionChanged():
        return scrollDirectionChanged(_that);
      case SwitchCategory():
        return switchCategory(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InitHome value)? init,
    TResult? Function(UpdateScroll value)? updateScroll,
    TResult? Function(UpdateHomeList value)? updateLoadedList,
    TResult? Function(LoadHomeData value)? loadHomeData,
    TResult? Function(LoadDeepLink value)? deepLinkFound,
    TResult? Function(ScrollDirectionChanged value)? scrollDirectionChanged,
    TResult? Function(SwitchCategory value)? switchCategory,
  }) {
    final _that = this;
    switch (_that) {
      case InitHome() when init != null:
        return init(_that);
      case UpdateScroll() when updateScroll != null:
        return updateScroll(_that);
      case UpdateHomeList() when updateLoadedList != null:
        return updateLoadedList(_that);
      case LoadHomeData() when loadHomeData != null:
        return loadHomeData(_that);
      case LoadDeepLink() when deepLinkFound != null:
        return deepLinkFound(_that);
      case ScrollDirectionChanged() when scrollDirectionChanged != null:
        return scrollDirectionChanged(_that);
      case SwitchCategory() when switchCategory != null:
        return switchCategory(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool scroll)? updateScroll,
    TResult Function(
            List<CategoryEntity>? categorySections,
            List<CategoryEntity>? categories,
            List<CategoryEntity>? subCategories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult Function()? loadHomeData,
    TResult Function(String route, Map<String, dynamic> args)? deepLinkFound,
    TResult Function(ScrollDirection direction)? scrollDirectionChanged,
    TResult Function(CategoryEntity? category, int index)? switchCategory,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case InitHome() when init != null:
        return init();
      case UpdateScroll() when updateScroll != null:
        return updateScroll(_that.scroll);
      case UpdateHomeList() when updateLoadedList != null:
        return updateLoadedList(
            _that.categorySections,
            _that.categories,
            _that.subCategories,
            _that.previouslyBought,
            _that.mostPopular,
            _that.mostBought,
            _that.banners);
      case LoadHomeData() when loadHomeData != null:
        return loadHomeData();
      case LoadDeepLink() when deepLinkFound != null:
        return deepLinkFound(_that.route, _that.args);
      case ScrollDirectionChanged() when scrollDirectionChanged != null:
        return scrollDirectionChanged(_that.direction);
      case SwitchCategory() when switchCategory != null:
        return switchCategory(_that.category, _that.index);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool scroll) updateScroll,
    required TResult Function(
            List<CategoryEntity>? categorySections,
            List<CategoryEntity>? categories,
            List<CategoryEntity>? subCategories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)
        updateLoadedList,
    required TResult Function() loadHomeData,
    required TResult Function(String route, Map<String, dynamic> args)
        deepLinkFound,
    required TResult Function(ScrollDirection direction) scrollDirectionChanged,
    required TResult Function(CategoryEntity? category, int index)
        switchCategory,
  }) {
    final _that = this;
    switch (_that) {
      case InitHome():
        return init();
      case UpdateScroll():
        return updateScroll(_that.scroll);
      case UpdateHomeList():
        return updateLoadedList(
            _that.categorySections,
            _that.categories,
            _that.subCategories,
            _that.previouslyBought,
            _that.mostPopular,
            _that.mostBought,
            _that.banners);
      case LoadHomeData():
        return loadHomeData();
      case LoadDeepLink():
        return deepLinkFound(_that.route, _that.args);
      case ScrollDirectionChanged():
        return scrollDirectionChanged(_that.direction);
      case SwitchCategory():
        return switchCategory(_that.category, _that.index);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(bool scroll)? updateScroll,
    TResult? Function(
            List<CategoryEntity>? categorySections,
            List<CategoryEntity>? categories,
            List<CategoryEntity>? subCategories,
            List<ProductEntity>? previouslyBought,
            List<ProductEntity>? mostPopular,
            List<ProductEntity>? mostBought,
            List<BannerEntity>? banners)?
        updateLoadedList,
    TResult? Function()? loadHomeData,
    TResult? Function(String route, Map<String, dynamic> args)? deepLinkFound,
    TResult? Function(ScrollDirection direction)? scrollDirectionChanged,
    TResult? Function(CategoryEntity? category, int index)? switchCategory,
  }) {
    final _that = this;
    switch (_that) {
      case InitHome() when init != null:
        return init();
      case UpdateScroll() when updateScroll != null:
        return updateScroll(_that.scroll);
      case UpdateHomeList() when updateLoadedList != null:
        return updateLoadedList(
            _that.categorySections,
            _that.categories,
            _that.subCategories,
            _that.previouslyBought,
            _that.mostPopular,
            _that.mostBought,
            _that.banners);
      case LoadHomeData() when loadHomeData != null:
        return loadHomeData();
      case LoadDeepLink() when deepLinkFound != null:
        return deepLinkFound(_that.route, _that.args);
      case ScrollDirectionChanged() when scrollDirectionChanged != null:
        return scrollDirectionChanged(_that.direction);
      case SwitchCategory() when switchCategory != null:
        return switchCategory(_that.category, _that.index);
      case _:
        return null;
    }
  }
}

/// @nodoc

class InitHome implements HomeEvent {
  const InitHome();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is InitHome);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HomeEvent.init()';
  }
}

/// @nodoc

class UpdateScroll implements HomeEvent {
  const UpdateScroll(this.scroll);

  final bool scroll;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateScrollCopyWith<UpdateScroll> get copyWith =>
      _$UpdateScrollCopyWithImpl<UpdateScroll>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateScroll &&
            (identical(other.scroll, scroll) || other.scroll == scroll));
  }

  @override
  int get hashCode => Object.hash(runtimeType, scroll);

  @override
  String toString() {
    return 'HomeEvent.updateScroll(scroll: $scroll)';
  }
}

/// @nodoc
abstract mixin class $UpdateScrollCopyWith<$Res>
    implements $HomeEventCopyWith<$Res> {
  factory $UpdateScrollCopyWith(
          UpdateScroll value, $Res Function(UpdateScroll) _then) =
      _$UpdateScrollCopyWithImpl;
  @useResult
  $Res call({bool scroll});
}

/// @nodoc
class _$UpdateScrollCopyWithImpl<$Res> implements $UpdateScrollCopyWith<$Res> {
  _$UpdateScrollCopyWithImpl(this._self, this._then);

  final UpdateScroll _self;
  final $Res Function(UpdateScroll) _then;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? scroll = null,
  }) {
    return _then(UpdateScroll(
      null == scroll
          ? _self.scroll
          : scroll // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class UpdateHomeList implements HomeEvent {
  const UpdateHomeList(
      {final List<CategoryEntity>? categorySections,
      final List<CategoryEntity>? categories,
      final List<CategoryEntity>? subCategories,
      final List<ProductEntity>? previouslyBought,
      final List<ProductEntity>? mostPopular,
      final List<ProductEntity>? mostBought,
      final List<BannerEntity>? banners})
      : _categorySections = categorySections,
        _categories = categories,
        _subCategories = subCategories,
        _previouslyBought = previouslyBought,
        _mostPopular = mostPopular,
        _mostBought = mostBought,
        _banners = banners;

  final List<CategoryEntity>? _categorySections;
  List<CategoryEntity>? get categorySections {
    final value = _categorySections;
    if (value == null) return null;
    if (_categorySections is EqualUnmodifiableListView)
      return _categorySections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<CategoryEntity>? _categories;
  List<CategoryEntity>? get categories {
    final value = _categories;
    if (value == null) return null;
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<CategoryEntity>? _subCategories;
  List<CategoryEntity>? get subCategories {
    final value = _subCategories;
    if (value == null) return null;
    if (_subCategories is EqualUnmodifiableListView) return _subCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _previouslyBought;
  List<ProductEntity>? get previouslyBought {
    final value = _previouslyBought;
    if (value == null) return null;
    if (_previouslyBought is EqualUnmodifiableListView)
      return _previouslyBought;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _mostPopular;
  List<ProductEntity>? get mostPopular {
    final value = _mostPopular;
    if (value == null) return null;
    if (_mostPopular is EqualUnmodifiableListView) return _mostPopular;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductEntity>? _mostBought;
  List<ProductEntity>? get mostBought {
    final value = _mostBought;
    if (value == null) return null;
    if (_mostBought is EqualUnmodifiableListView) return _mostBought;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<BannerEntity>? _banners;
  List<BannerEntity>? get banners {
    final value = _banners;
    if (value == null) return null;
    if (_banners is EqualUnmodifiableListView) return _banners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateHomeListCopyWith<UpdateHomeList> get copyWith =>
      _$UpdateHomeListCopyWithImpl<UpdateHomeList>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateHomeList &&
            const DeepCollectionEquality()
                .equals(other._categorySections, _categorySections) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._subCategories, _subCategories) &&
            const DeepCollectionEquality()
                .equals(other._previouslyBought, _previouslyBought) &&
            const DeepCollectionEquality()
                .equals(other._mostPopular, _mostPopular) &&
            const DeepCollectionEquality()
                .equals(other._mostBought, _mostBought) &&
            const DeepCollectionEquality().equals(other._banners, _banners));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_categorySections),
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_subCategories),
      const DeepCollectionEquality().hash(_previouslyBought),
      const DeepCollectionEquality().hash(_mostPopular),
      const DeepCollectionEquality().hash(_mostBought),
      const DeepCollectionEquality().hash(_banners));

  @override
  String toString() {
    return 'HomeEvent.updateLoadedList(categorySections: $categorySections, categories: $categories, subCategories: $subCategories, previouslyBought: $previouslyBought, mostPopular: $mostPopular, mostBought: $mostBought, banners: $banners)';
  }
}

/// @nodoc
abstract mixin class $UpdateHomeListCopyWith<$Res>
    implements $HomeEventCopyWith<$Res> {
  factory $UpdateHomeListCopyWith(
          UpdateHomeList value, $Res Function(UpdateHomeList) _then) =
      _$UpdateHomeListCopyWithImpl;
  @useResult
  $Res call(
      {List<CategoryEntity>? categorySections,
      List<CategoryEntity>? categories,
      List<CategoryEntity>? subCategories,
      List<ProductEntity>? previouslyBought,
      List<ProductEntity>? mostPopular,
      List<ProductEntity>? mostBought,
      List<BannerEntity>? banners});
}

/// @nodoc
class _$UpdateHomeListCopyWithImpl<$Res>
    implements $UpdateHomeListCopyWith<$Res> {
  _$UpdateHomeListCopyWithImpl(this._self, this._then);

  final UpdateHomeList _self;
  final $Res Function(UpdateHomeList) _then;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? categorySections = freezed,
    Object? categories = freezed,
    Object? subCategories = freezed,
    Object? previouslyBought = freezed,
    Object? mostPopular = freezed,
    Object? mostBought = freezed,
    Object? banners = freezed,
  }) {
    return _then(UpdateHomeList(
      categorySections: freezed == categorySections
          ? _self._categorySections
          : categorySections // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      categories: freezed == categories
          ? _self._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      subCategories: freezed == subCategories
          ? _self._subCategories
          : subCategories // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      previouslyBought: freezed == previouslyBought
          ? _self._previouslyBought
          : previouslyBought // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      mostPopular: freezed == mostPopular
          ? _self._mostPopular
          : mostPopular // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      mostBought: freezed == mostBought
          ? _self._mostBought
          : mostBought // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
      banners: freezed == banners
          ? _self._banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<BannerEntity>?,
    ));
  }
}

/// @nodoc

class LoadHomeData implements HomeEvent {
  const LoadHomeData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LoadHomeData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HomeEvent.loadHomeData()';
  }
}

/// @nodoc

class LoadDeepLink implements HomeEvent {
  const LoadDeepLink(this.route, final Map<String, dynamic> args)
      : _args = args;

  final String route;
  final Map<String, dynamic> _args;
  Map<String, dynamic> get args {
    if (_args is EqualUnmodifiableMapView) return _args;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_args);
  }

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoadDeepLinkCopyWith<LoadDeepLink> get copyWith =>
      _$LoadDeepLinkCopyWithImpl<LoadDeepLink>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoadDeepLink &&
            (identical(other.route, route) || other.route == route) &&
            const DeepCollectionEquality().equals(other._args, _args));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, route, const DeepCollectionEquality().hash(_args));

  @override
  String toString() {
    return 'HomeEvent.deepLinkFound(route: $route, args: $args)';
  }
}

/// @nodoc
abstract mixin class $LoadDeepLinkCopyWith<$Res>
    implements $HomeEventCopyWith<$Res> {
  factory $LoadDeepLinkCopyWith(
          LoadDeepLink value, $Res Function(LoadDeepLink) _then) =
      _$LoadDeepLinkCopyWithImpl;
  @useResult
  $Res call({String route, Map<String, dynamic> args});
}

/// @nodoc
class _$LoadDeepLinkCopyWithImpl<$Res> implements $LoadDeepLinkCopyWith<$Res> {
  _$LoadDeepLinkCopyWithImpl(this._self, this._then);

  final LoadDeepLink _self;
  final $Res Function(LoadDeepLink) _then;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? route = null,
    Object? args = null,
  }) {
    return _then(LoadDeepLink(
      null == route
          ? _self.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      null == args
          ? _self._args
          : args // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class ScrollDirectionChanged implements HomeEvent {
  const ScrollDirectionChanged(this.direction);

  final ScrollDirection direction;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ScrollDirectionChangedCopyWith<ScrollDirectionChanged> get copyWith =>
      _$ScrollDirectionChangedCopyWithImpl<ScrollDirectionChanged>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ScrollDirectionChanged &&
            (identical(other.direction, direction) ||
                other.direction == direction));
  }

  @override
  int get hashCode => Object.hash(runtimeType, direction);

  @override
  String toString() {
    return 'HomeEvent.scrollDirectionChanged(direction: $direction)';
  }
}

/// @nodoc
abstract mixin class $ScrollDirectionChangedCopyWith<$Res>
    implements $HomeEventCopyWith<$Res> {
  factory $ScrollDirectionChangedCopyWith(ScrollDirectionChanged value,
          $Res Function(ScrollDirectionChanged) _then) =
      _$ScrollDirectionChangedCopyWithImpl;
  @useResult
  $Res call({ScrollDirection direction});
}

/// @nodoc
class _$ScrollDirectionChangedCopyWithImpl<$Res>
    implements $ScrollDirectionChangedCopyWith<$Res> {
  _$ScrollDirectionChangedCopyWithImpl(this._self, this._then);

  final ScrollDirectionChanged _self;
  final $Res Function(ScrollDirectionChanged) _then;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? direction = null,
  }) {
    return _then(ScrollDirectionChanged(
      null == direction
          ? _self.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as ScrollDirection,
    ));
  }
}

/// @nodoc

class SwitchCategory implements HomeEvent {
  const SwitchCategory(this.category, this.index);

  final CategoryEntity? category;
  final int index;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SwitchCategoryCopyWith<SwitchCategory> get copyWith =>
      _$SwitchCategoryCopyWithImpl<SwitchCategory>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SwitchCategory &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, category, index);

  @override
  String toString() {
    return 'HomeEvent.switchCategory(category: $category, index: $index)';
  }
}

/// @nodoc
abstract mixin class $SwitchCategoryCopyWith<$Res>
    implements $HomeEventCopyWith<$Res> {
  factory $SwitchCategoryCopyWith(
          SwitchCategory value, $Res Function(SwitchCategory) _then) =
      _$SwitchCategoryCopyWithImpl;
  @useResult
  $Res call({CategoryEntity? category, int index});
}

/// @nodoc
class _$SwitchCategoryCopyWithImpl<$Res>
    implements $SwitchCategoryCopyWith<$Res> {
  _$SwitchCategoryCopyWithImpl(this._self, this._then);

  final SwitchCategory _self;
  final $Res Function(SwitchCategory) _then;

  /// Create a copy of HomeEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? category = freezed,
    Object? index = null,
  }) {
    return _then(SwitchCategory(
      freezed == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryEntity?,
      null == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
