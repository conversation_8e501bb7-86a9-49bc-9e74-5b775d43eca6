import 'dart:ui';

import 'package:flutter/material.dart';

class GradientBorderContainer extends StatelessWidget {
  final double width;
  final double height;
  final Color? containerColor;
  final Gradient? containerGradient;
  final Color topBorderColor;
  final Color bottomBorderColor;
  final double borderWidth;
  final BorderRadius? borderRadius;
  final Widget? child;

  const GradientBorderContainer({
    super.key,
    required this.width,
    required this.height,
    this.containerColor,
    required this.topBorderColor,
    required this.bottomBorderColor,
    this.borderWidth = 2.0,
    this.borderRadius,
    this.child,
    this.containerGradient,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: containerColor,
        gradient: containerGradient,
        borderRadius: borderRadius,
      ),
      child: CustomPaint(
        painter: _GradientBorderPainter(
          topBorderColor: topBorderColor,
          bottomBorderColor: bottomBorderColor,
          borderWidth: borderWidth,
          borderRadius: borderRadius,
        ),
        child: child, // Pass the child directly to <PERSON><PERSON><PERSON><PERSON>'s child
      ),
    );
  }
}

class _GradientBorderPainter extends CustomPainter {
  final Color topBorderColor;
  final Color bottomBorderColor;
  final double borderWidth;
  final BorderRadius? borderRadius;

  _GradientBorderPainter({
    required this.topBorderColor,
    required this.bottomBorderColor,
    required this.borderWidth,
    this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Rect rect = Offset.zero & size; // The bounds of the container

    final Paint paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    // Create the LinearGradient shader
    // The gradient runs from topCenter to bottomCenter of the rect
    paint.shader = LinearGradient(
      colors: [topBorderColor, bottomBorderColor],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ).createShader(
        rect); // Important: Create the shader relative to the rect's bounds

    // Draw the border based on whether borderRadius is provided
    if (borderRadius != null) {
      final RRect rrect = RRect.fromRectAndCorners(
        rect,
        topLeft: borderRadius!.topLeft,
        topRight: borderRadius!.topRight,
        bottomLeft: borderRadius!.bottomLeft,
        bottomRight: borderRadius!.bottomRight,
      );
      canvas.drawRRect(rrect, paint);
    } else {
      canvas.drawRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _GradientBorderPainter oldDelegate) {
    // Repaint if any relevant property changes
    return oldDelegate.topBorderColor != topBorderColor ||
        oldDelegate.bottomBorderColor != bottomBorderColor ||
        oldDelegate.borderWidth != borderWidth ||
        oldDelegate.borderRadius != borderRadius;
  }
}
