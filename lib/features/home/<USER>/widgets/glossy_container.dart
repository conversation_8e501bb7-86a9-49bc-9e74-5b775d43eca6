import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:rozana/widgets/custom_image.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_text.dart';

class GlossyContainer extends StatelessWidget {
  const GlossyContainer(
      {super.key, required this.width, required this.height, this.child});

  final double width;
  final double height;

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(11),
            color: AppColors.primary,
            boxShadow: [
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.6),
                offset: const Offset(-0.9, -0.3),
                blurRadius: 0,
                spreadRadius: 0.4,
              ),
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.6),
                offset: const Offset(1.2, -0.3),
                blurRadius: 0,
                spreadRadius: 0,
              ),
              BoxShadow(
                color: AppColors.primary,
                offset: const Offset(-1, 5),
                blurRadius: 5,
                spreadRadius: 1,
              ),
              BoxShadow(
                color: AppColors.primary,
                offset: const Offset(1, 5),
                blurRadius: 5,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            gradient: RadialGradient(
              colors: [
                Color(0xFF0C134F),
                Colors.white,
                Colors.white,
              ],
              center: Alignment.bottomCenter,
              radius: 20,
            ),
          ),
          alignment: Alignment.center,
          child: child,
        ),
      ],
    );
  }
}

class AppBarCategoryBox extends StatelessWidget {
  const AppBarCategoryBox(
      {super.key,
      required this.imagePath,
      this.onTap,
      required this.isSelected,
      required this.categoryName});

  final String imagePath;
  final void Function()? onTap;
  final bool isSelected;
  final String categoryName;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return GestureDetector(
      onTap: onTap,
      child: IntrinsicWidth(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // GradientBorderContainer(
            //   height: (screenWidth - 250) / 3.2,
            //   width: (screenWidth - 250) / 3.2,
            //   // containerColor: Colors.white.withValues(alpha: 0.06),
            //   containerGradient: LinearGradient(colors: [
            //     Colors.white.withValues(alpha: 0.3),
            //     AppColors.primary.withValues(alpha: 0.1),
            //   ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
            //   topBorderColor: Colors.white,
            //   bottomBorderColor: AppColors.primary,
            //   borderWidth: 1.4,
            //   borderRadius: BorderRadius.circular(10.0),
            //   child: Padding(
            //     padding: const EdgeInsets.all(8),
            //     child: Image.asset(
            //       imagePath,
            //     ),
            //   ),
            // ),
            Container(
              height: (screenWidth - 210) / 3.2,
              width: (screenWidth - 210) / 3.2,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: CustomImage(
                  imageUrl: imagePath,
                ),
              ),
            ),

            SizedBox(height: 6),
            CustomText(
              categoryName,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              color: isSelected ? AppColors.white : Color(0xFFE1E1E1),
            ),
            // Divider(
            //   color: isSelected ? AppColors.white : AppColors.primary,
            //   thickness: 3,
            //   height: 10,
            //   radius: BorderRadius.circular(20),
            // ),
            Container(
              height: 3,
              margin: EdgeInsets.only(top: 3),
              decoration: BoxDecoration(
                  color: isSelected ? Colors.white : Colors.transparent,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20))),
            )
          ],
        ),
      ),
    );
  }
}
