import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/utils/helpers.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../widgets/custom_text.dart';

class RefreshableAddressBar extends StatelessWidget {
  final double height;
  final EdgeInsetsGeometry padding;
  final Color backgroundColor;
  final Color textColor;
  final ValueChanged<AddressModel?>? onAddressChanged;

  const RefreshableAddressBar({
    super.key,
    this.height = 60,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.backgroundColor = Colors.transparent,
    this.textColor = Colors.white,
    this.onAddressChanged,
  });

  void _handleTap(BuildContext context) async {
    // Check if user is authenticated first
    final appState = context.read<AppBloc>().state;
    final isAuthenticated = appState.maybeMap(
      loaded: (loaded) => loaded.isAuthenticated,
      orElse: () => false,
    );

    if (!isAuthenticated) {
      // Redirect to login screen with return path to addresses
      if (context.mounted) {
        // context.push(RouteNames.login, extra: {
        //   'returnRoute': RouteNames.addresses,
        // });
        context
            .push(
          RouteNames.mapForNewAddress,
        )
            .then((_) {
          // _loadAddresses();
        });
      }
      return;
    }

    // For authenticated users, navigate to address list or add address screen
    final addressService = context.read<LocationBloc>().addressService;
    final addresses =
        await addressService.getAllAddresses(); // Uses cached data

    // Check if the widget is still mounted before using context
    if (context.mounted) {
      if (addresses.isEmpty) {
        context.push(RouteNames.mapForNewAddress);
      } else {
        // Pass onAddressSelected callback to automatically return after selection
        context.push(RouteNames.addresses, extra: {
          'onAddressSelected': (AddressModel selectedAddress) {
            final locationBloc = context.read<LocationBloc>();
            locationBloc.refreshUI();
            context.pop();
          },
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LocationBloc, LocationState>(
      listener: (context, state) {
        state.maybeWhen(
          loaded: (address) {
            if (onAddressChanged != null) {
              onAddressChanged!(address);
            }
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        String address = '';
        String addressType = '';
        bool isRefreshing = false;
        state.maybeWhen(
          loaded: (addressData) {
            address = addressData.fullAddress ?? '';
            addressType = addressData.addressType ?? 'Home';
          },
          loading: () {
            address = 'Detecting location...';
          },
          notServiceable: (addressData) {
            // Show the address even when it's not serviceable
            address = addressData.fullAddress ?? '';
            addressType = addressData.addressType ?? 'Location';
          },
          orElse: () {
            address = 'Select an address';
          },
        );

        return InkWell(
          onTap: () => _handleTap(context),
          child: Container(
            // height: height,
            padding: AppDimensions.screenPadding.copyWith(top: 0, bottom: 0),
            color: backgroundColor,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 3),
                  child: Image.asset(
                    'assets/icons/home_location.png',
                    height: 26,
                    width: 26,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CustomText(
                            TextFormatter.formatToUiText(addressType),
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: AppColors.background,
                          ),
                          SizedBox(width: 7),
                          Flexible(
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 7, vertical: 4),
                              decoration: BoxDecoration(
                                color: Color(0xFFF1F2DA),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: CustomText(
                                'Deliver in 1 Hour',
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textPrimary,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4),
                      CustomText(
                        address,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        overflow: TextOverflow.ellipsis,
                        color: AppColors.background,
                      ),
                    ],
                  ),
                ),
                if (addressType.toLowerCase() == 'current')
                  StatefulBuilder(builder: (context, setState) {
                    return CircleAvatar(
                      backgroundColor: AppColors.primary.withValues(alpha: 0.2),
                      child: IconButton(
                        icon: isRefreshing
                            ? SizedBox(
                                width: 14,
                                height: 14,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppColors.white,
                                ),
                              )
                            : Icon(
                                Icons.refresh,
                                size: 16,
                                color: AppColors.white,
                              ),
                        onPressed: () {
                          if (!isRefreshing) {
                            setState(
                              () {
                                isRefreshing = true;
                              },
                            );
                            getIt<LocationBloc>()
                                .add(LocationEvent.refreshLocation());
                          }
                        },
                        padding: EdgeInsets.zero,
                        tooltip: 'Refresh location',
                        constraints: const BoxConstraints(),
                      ),
                    );
                  }),
              ],
            ),
          ),
        );
      },
    );
  }
}
