import 'dart:ui';

import 'package:flutter/material.dart';

class RunningBorderContainer extends StatefulWidget {
  final double width;
  final double height;
  final Color containerColor;
  final Color borderColor;
  final Color runningColor;
  final Color flashColor; // <<< NEW PROPERTY
  final double runningLength; // Percentage of the border length
  final Duration animationDuration; // Duration for one full run
  final Duration flashDuration; // Duration of the full border flash
  final int cyclesBeforeFlash; // Number of running cycles before a flash
  final BorderRadius? borderRadius;
  final Widget? child;

  const RunningBorderContainer({
    super.key,
    required this.width,
    required this.height,
    required this.containerColor,
    required this.borderColor,
    required this.runningColor,
    required this.flashColor, // <<< REQUIRED
    this.runningLength = 0.1,
    this.animationDuration = const Duration(seconds: 2),
    this.flashDuration = const Duration(milliseconds: 300),
    this.cyclesBeforeFlash = 2, // Default to 2 cycles
    this.borderRadius,
    this.child,
  });

  @override
  _RunningBorderContainerState createState() => _RunningBorderContainerState();
}

class _RunningBorderContainerState extends State<RunningBorderContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _runningAnimation;

  bool _isFlashing = false;
  int _completedCycles = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _runningAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _completedCycles++;
        if (_completedCycles % widget.cyclesBeforeFlash == 0) {
          setState(() {
            _isFlashing = true;
          });
          Future.delayed(widget.flashDuration, () {
            setState(() {
              _isFlashing = false;
            });
            _animationController.forward(from: 0.0);
          });
        } else {
          _animationController.forward(from: 0.0);
        }
      }
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.containerColor,
            borderRadius: widget.borderRadius,
          ),
          child: CustomPaint(
            painter: _RunningBorderPainter(
              animationValue: _runningAnimation.value,
              borderColor: widget.borderColor,
              runningColor: widget.runningColor,
              flashColor: widget.flashColor, // <<< PASS NEW PROPERTY
              runningLength: widget.runningLength,
              borderRadius: widget.borderRadius,
              isFlashing: _isFlashing,
            ),
            child: SizedBox(
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

class _RunningBorderPainter extends CustomPainter {
  final double animationValue;
  final Color borderColor;
  final Color runningColor;
  final Color flashColor; // <<< NEW PROPERTY
  final double runningLength;
  final BorderRadius? borderRadius;
  final bool isFlashing;

  _RunningBorderPainter({
    required this.animationValue,
    required this.borderColor,
    required this.runningColor,
    required this.flashColor, // <<< REQUIRED
    required this.runningLength,
    this.borderRadius,
    this.isFlashing = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Define the paint for the normal border
    final Paint normalBorderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.4; // Normal border thickness

    // Define the paint for the running part
    final Paint runningPartPaint = Paint()
      ..color = runningColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.4; // Running part thickness

    // Define the paint for the flash
    final Paint flashPaint = Paint() // <<< DEDICATED FLASH PAINT
      ..color = flashColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.6; // Flash thickness (matching running part thickness)

    final double totalLength = (size.width * 2) + (size.height * 2);
    final double actualRunningLength = totalLength * runningLength;

    final Rect rect = Offset.zero & size;

    RRect? rrect;
    if (borderRadius != null) {
      rrect = RRect.fromRectAndCorners(
        rect,
        topLeft: borderRadius!.topLeft,
        topRight: borderRadius!.topRight,
        bottomLeft: borderRadius!.bottomLeft,
        bottomRight: borderRadius!.bottomRight,
      );
    }

    Path borderPath = Path();
    if (rrect != null) {
      borderPath.addRRect(rrect);
    } else {
      borderPath.addRect(rect);
    }

    // --- Core Logic for Drawing Border Based on Flash State ---
    if (isFlashing) {
      // If flashing, draw the entire border using the dedicated flashPaint
      canvas.drawPath(borderPath, flashPaint);
    } else {
      // If not flashing, first draw the normal border
      canvas.drawPath(borderPath, normalBorderPaint);

      // Then, draw the running part on top
      final double start = totalLength * animationValue;
      final double end = start + actualRunningLength;

      final PathMetrics pathMetrics = borderPath.computeMetrics();
      for (PathMetric pathMetric in pathMetrics) {
        if (end <= pathMetric.length) {
          final Tangent? tangentStart = pathMetric.getTangentForOffset(start);
          final Tangent? tangentEnd = pathMetric.getTangentForOffset(end);

          if (tangentStart != null && tangentEnd != null) {
            final Path segmentPath = pathMetric.extractPath(start, end);
            canvas.drawPath(segmentPath,
                runningPartPaint); // Use runningPartPaint for the segment
          }
        } else if (start < pathMetric.length && end > pathMetric.length) {
          final Path segment1 =
              pathMetric.extractPath(start, pathMetric.length);
          canvas.drawPath(segment1, runningPartPaint);

          final PathMetric? firstMetric =
              borderPath.computeMetrics().firstOrNull;
          if (firstMetric != null) {
            final Path segment2 =
                firstMetric.extractPath(0, end - pathMetric.length);
            canvas.drawPath(segment2, runningPartPaint);
          }
        } else if (start >= pathMetric.length && end > totalLength) {
          final double wrapAroundStart = start % totalLength;
          final double wrapAroundEnd =
              (start + actualRunningLength) % totalLength;

          if (wrapAroundEnd > wrapAroundStart) {
            final Path segment =
                pathMetric.extractPath(wrapAroundStart, wrapAroundEnd);
            canvas.drawPath(segment, runningPartPaint);
          } else {
            final Path segment1 =
                pathMetric.extractPath(wrapAroundStart, pathMetric.length);
            canvas.drawPath(segment1, runningPartPaint);
            final PathMetric? firstMetric =
                borderPath.computeMetrics().firstOrNull;
            if (firstMetric != null) {
              final Path segment2 = firstMetric.extractPath(0, wrapAroundEnd);
              canvas.drawPath(segment2, runningPartPaint);
            }
          }
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant _RunningBorderPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
        oldDelegate.borderColor != borderColor ||
        oldDelegate.runningColor != runningColor ||
        oldDelegate.flashColor != flashColor || // <<< INCLUDE NEW PROPERTY
        oldDelegate.runningLength != runningLength ||
        oldDelegate.borderRadius != borderRadius ||
        oldDelegate.isFlashing != isFlashing;
  }
}
