import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/features/home/<USER>/widgets/header_widget.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../products/presentation/widgets/product_card.dart';
import '../../bloc/home bloc/home_bloc.dart';

class BudgetBazaarSection extends StatelessWidget {
  const BudgetBazaarSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.mostBought != current.mostBought;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<ProductEntity>? mostBought =
            state.mapOrNull(loaded: (value) => value.mostBought);
        return Visibility(
          visible: mostBought != null,
          child: Visibility(
              visible: mostBought?.isNotEmpty ?? false,
              child: Column(
                children: [
                  HeaderWidget(title: 'Budget Bazaar'),
                  SizedBox(height: 16),
                  GridView.builder(
                    primary: false,
                    shrinkWrap: true,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.8,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.screenHzPadding),
                    itemCount: mostBought?.length,
                    itemBuilder: (ctx, index) {
                      return SizedBox(
                        width: MediaQuery.of(context).size.width * 0.35,
                        child: DiscountedProductCard(
                          height:
                              (((MediaQuery.of(context).size.height - 40) / 2) *
                                      0.8) -
                                  80,
                          product: mostBought?[index],
                          isLoading: mostBought == null,
                        ),
                      );
                    },
                  )
                ],
              )),
        );
      },
    );
  }
}
