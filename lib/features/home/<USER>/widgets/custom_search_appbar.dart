import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/blocs/language_bloc/language_bloc.dart';
import 'package:rozana/core/extensions/localization_extension.dart';

import '../../../../routes/app_router.dart';
import 'refreshable_address_bar.dart';

class CustomSliverAppBarContent extends StatelessWidget {
  const CustomSliverAppBarContent({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Column(
        mainAxisSize: MainAxisSize.min, // Make column as small as possible
        children: [
          // TOP LOCATION/DELIVERY BAR
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: RefreshableAddressBar(
                    // height: 42,
                    backgroundColor: Colors.transparent,
                    textColor: AppColors.white,
                    onAddressChanged: (address) {}),
              ),
              const SizedBox(width: 10),
              BlocBuilder<LanguageBloc, LanguageState>(
                builder: (context, languageState) {
                  return Tooltip(
                    message: context.l10n.language,
                    child: InkWell(
                      onTap: () {
                        // Toggle between English and Hindi
                        context
                            .read<LanguageBloc>()
                            .add(const LanguageEvent.toggleLanguage());
                        HapticFeedback.lightImpact();

                        // Show feedback to user
                        final currentLanguage = languageState.mapOrNull(
                              loaded: (state) => state.languageCode,
                            ) ??
                            'en';
                        final newLanguage =
                            currentLanguage == 'en' ? 'hi' : 'en';
                        final languageName =
                            newLanguage == 'en' ? 'English' : 'हिंदी';

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Language switched to $languageName',
                              style: const TextStyle(color: AppColors.white),
                            ),
                            backgroundColor: AppColors.primary,
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Image.asset(
                          'assets/icons/language_icon.png',
                          height: 25,
                          width: 25,
                        ),
                      ),
                    ),
                  );
                },
              ),
              Tooltip(
                message: 'Profile',
                child: InkWell(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    context.push(RouteNames.profile);
                  },
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 10, 8, 10),
                    child: Image.asset(
                      'assets/icons/user_profile.png',
                      height: 25,
                      width: 25,
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.screenHzPadding),
            ],
          ),
          SizedBox(height: 4),

          // // SEARCH BAR
          // GestureDetector(
          //   onTap: () {
          //     context.push('${RouteNames.search}?initialQuery=');
          //   },
          //   child: Container(
          //     height: 48,
          //     margin: EdgeInsets.symmetric(
          //         vertical: 10, horizontal: AppDimensions.screenHzPadding),
          //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          //     decoration: BoxDecoration(
          //       color: Colors.white,
          //       borderRadius: BorderRadius.circular(12),
          //     ),
          //     child: Row(
          //       children: [
          //         Image.asset(
          //           'assets/icons/search_icon.png',
          //           width: 20,
          //           height: 20,
          //         ),
          //         const SizedBox(width: 8),
          //         Expanded(
          //           child: CustomText(
          //             "Search for 'milk'",
          //             color: AppColors.textGrey,
          //             fontSize: 14,
          //             maxLines: 1,
          //             fontWeight: FontWeight.w500,
          //             overflow: TextOverflow.ellipsis,
          //             textHeight: 0.9,
          //           ),
          //         ),
          //         // Image.asset(
          //         //   'assets/icons/mic_icon.png',
          //         //   width: 14,
          //         //   height: 14,
          //         // ),
          //       ],
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
