import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/viewall_category_title.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_dimensions.dart';
import '../../../../routes/app_router.dart';
import '../../bloc/home bloc/home_bloc.dart';

class DealsSection extends StatelessWidget {
  const DealsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.categories != current.categories;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<CategoryEntity>? categories =
            state.mapOrNull(loaded: (value) => value.categories);
        return Visibility(
          visible: ((categories?.length ?? 0) >= 3),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(
                    left: AppDimensions.screenHzPadding,
                    right: AppDimensions.screenHzPadding - 15,
                    bottom: 10),
                child: ViewAllCategoryTitle(
                  title: 'Deals',
                  onTap: () {
                    HapticFeedback.lightImpact();
                    context.push(RouteNames.categories);
                  },
                  offerLabel: Container(
                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    margin: EdgeInsets.only(left: 10, top: 4),
                    decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(2),
                        border: Border.all(
                          color: AppColors.surface,
                        )),
                    child: FittedBox(
                      child: RichText(
                          text: TextSpan(
                              text: 'Starts from ',
                              children: [
                                TextSpan(
                                  text: '₹ ${99}/-',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w800,
                                  ),
                                ),
                              ],
                              style: TextStyle(
                                color: AppColors.surface,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ))),
                    ),
                  ),
                ),
              ),
              if ((categories?.length ?? 0) >= 3)
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.screenHzPadding),
                  child: Row(
                      spacing: 10,
                      children: List.generate(3, (i) {
                        CategoryEntity? category = categories?[i];
                        return Expanded(
                          flex: i == 2 ? 19 : 9,
                          child: GestureDetector(
                            onTap: () async {
                              HapticFeedback.lightImpact();
                              context.push(RouteNames.products, extra: {
                                'categoryId': category?.id,
                                'categoryName': category?.name,
                              });
                              // Log category view event to AppsFlyer
                              await AppsFlyerEvents.categoryView(
                                category?.id ?? '',
                                category?.name ?? '',
                              );
                            },
                            child: Stack(
                              alignment: Alignment.topCenter,
                              children: [
                                Container(
                                  height: 120,
                                  margin: EdgeInsets.only(top: 9),
                                  padding: EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFFEDA00),
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Column(
                                    children: [
                                      Flexible(
                                        child: SizedBox(
                                            width: double.infinity,
                                            child: CustomImage(
                                              imageUrl:
                                                  category?.imageUrl ?? '',
                                              fit: BoxFit.cover,
                                            )),
                                      ),
                                      SizedBox(height: 4),
                                      ConstrainedBox(
                                        constraints:
                                            BoxConstraints(minHeight: 20),
                                        child: Center(
                                          child: CustomText(
                                            category?.name ?? '',
                                            fontSize: 10,
                                            fontWeight: FontWeight.w800,
                                            maxLines: 2,
                                            textHeight: 1,
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                Visibility(
                                  visible: i == 2,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 5, vertical: 3),
                                    decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        borderRadius: BorderRadius.circular(2),
                                        border: Border.all(
                                          color: AppColors.surface,
                                        )),
                                    child: FittedBox(
                                      child: RichText(
                                          text: TextSpan(
                                              text:
                                                  '${category?.name} Combos at ',
                                              children: [
                                                TextSpan(
                                                  text: '₹ ${68}/-',
                                                  style: TextStyle(
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w800,
                                                  ),
                                                ),
                                              ],
                                              style: TextStyle(
                                                color: AppColors.surface,
                                                fontSize: 9,
                                                fontWeight: FontWeight.w500,
                                              ))),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      })),
                ),
              if ((categories?.length ?? 0) >= 6)
                Padding(
                  padding: const EdgeInsets.only(
                      left: AppDimensions.screenHzPadding,
                      top: 15,
                      right: AppDimensions.screenHzPadding),
                  child: Row(
                      spacing: 10,
                      children: List.generate(3, (i) {
                        i = i + 3;
                        CategoryEntity? category = categories?[i];
                        return Expanded(
                          flex: (i == 3) ? 19 : 9,
                          child: GestureDetector(
                            onTap: () async {
                              HapticFeedback.lightImpact();
                              context.push(RouteNames.products, extra: {
                                'categoryId': category?.id,
                                'categoryName': category?.name,
                              });
                              
                              // Log category view event to AppsFlyer
                              await AppsFlyerEvents.categoryView(
                                category?.id ?? '',
                                category?.name ?? '',
                              );
                            },
                            child: Stack(
                              alignment: Alignment.topCenter,
                              children: [
                                Container(
                                  height: 96,
                                  margin: EdgeInsets.only(top: 9),
                                  padding: EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFFEDA00),
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Column(
                                    children: [
                                      Flexible(
                                        child: SizedBox(
                                            width: double.infinity,
                                            child: CustomImage(
                                              imageUrl:
                                                  category?.imageUrl ?? '',
                                              fit: BoxFit.cover,
                                            )),
                                      ),
                                      SizedBox(height: 4),
                                      ConstrainedBox(
                                        constraints:
                                            BoxConstraints(minHeight: 20),
                                        child: Center(
                                          child: CustomText(
                                            category?.name ?? '',
                                            fontSize: 10,
                                            fontWeight: FontWeight.w800,
                                            maxLines: 2,
                                            textHeight: 1,
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                Visibility(
                                  visible: i == 3,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 5, vertical: 3),
                                    decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        borderRadius: BorderRadius.circular(2),
                                        border: Border.all(
                                          color: AppColors.surface,
                                        )),
                                    child: FittedBox(
                                      child: RichText(
                                          text: TextSpan(
                                              text:
                                                  '${category?.name} Combos at ',
                                              children: [
                                                TextSpan(
                                                  text: '₹ ${68}/-',
                                                  style: TextStyle(
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w800,
                                                  ),
                                                ),
                                              ],
                                              style: TextStyle(
                                                color: AppColors.surface,
                                                fontSize: 9,
                                                fontWeight: FontWeight.w500,
                                              ))),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      })),
                ),
              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }
}
