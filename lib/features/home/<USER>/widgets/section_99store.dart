import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/features/home/<USER>/widgets/header_widget.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../products/presentation/widgets/product_card.dart';
import '../../bloc/home bloc/home_bloc.dart';

class Section99store extends StatelessWidget {
  const Section99store({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.mostBought != current.mostBought;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<ProductEntity>? mostBought =
            state.mapOrNull(loaded: (value) => value.mostBought);
        return Visibility(
          visible: mostBought != null,
          child: Visibility(
              visible: mostBought?.isNotEmpty ?? false,
              child: Column(
                children: [
                  HeaderWidget(title: '₹199 Store'),
                  SizedBox(height: 16),
                  SizedBox(
                    height: 200,
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      shrinkWrap: true,
                      primary: false,
                      padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.screenHzPadding),
                      itemCount: mostBought?.length ?? 0,
                      itemBuilder: (context, index) {
                        return SizedBox(
                          width: MediaQuery.of(context).size.width * 0.35,
                          child: DiscountedProductCard(
                            height: 200,
                            product: mostBought?[index],
                            isLoading: mostBought == null,
                          ),
                        );
                      },
                      separatorBuilder: (context, index) => SizedBox(width: 12),
                    ),
                  )
                ],
              )),
        );
      },
    );
  }
}
