import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../domain/entities/product_entity.dart';
import '../../bloc/home bloc/home_bloc.dart';
import 'header_widget.dart';
import 'product_grid.dart';

class MostBoughtSection extends StatelessWidget {
  const MostBoughtSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.mostBought != current.mostBought;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<ProductEntity>? mostBought =
            state.mapOrNull(loaded: (value) => value.mostBought);
        // Use AnimatedSwitcher for smooth transitions between loading and loaded states
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          switchInCurve: Curves.easeInOut,
          switchOutCurve: Curves.easeInOut,
          child: mostBought == null
              ? RepaintBoundary(
                  child: Semantics(
                    label: 'Loading top products',
                    hint: 'Please wait while top products are loading',
                    child: Column(
                      key: const ValueKey('most_bought_skeleton'),
                      children: [
                        // Use enhanced shimmer with staggered loading for better visual feedback
                        ProductGrid(
                          productList: null,
                          useEnhancedShimmer: true,
                        ),
                      ],
                    ),
                  ),
                )
              : mostBought.isEmpty
                  ? const SizedBox
                      .shrink() // Don't show anything if list is empty
                  : RepaintBoundary(
                      child: Column(
                        key: const ValueKey('most_bought_loaded'),
                        children: [
                          // Padding(
                          //   padding: const EdgeInsets.only(
                          //       left: AppDimensions.screenHzPadding,
                          //       right: AppDimensions.screenHzPadding - 15),
                          //   child: ViewAllCategoryTitle(
                          //     showViewAll: false,
                          //     title: 'Top Products',
                          //     onTap: () {
                          //       HapticFeedback.lightImpact();
                          //     },
                          //   ),
                          // ),
                          HeaderWidget(title: "Top Products"),
                          const SizedBox(height: 20),
                          ProductGrid(productList: mostBought),
                        ],
                      ),
                    ),
        );
      },
    );
  }
}
