import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../categories/presentation/widgets/categoy_card.dart';
import '../../bloc/home bloc/home_bloc.dart';

class CategoriesSection extends StatelessWidget {
  const CategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        List<CategoryEntity> prevCategories =
            previous.mapOrNull(loaded: (value) => value.subCategories) ?? [];

        List<CategoryEntity> currCategories =
            current.mapOrNull(loaded: (value) => value.subCategories) ?? [];

        return prevCategories != currCategories;
      },
      builder: (context, state) {
        List<CategoryEntity>? subCategories =
            state.mapOrNull(loaded: (value) => value.subCategories);
        return Column(
          children: [
            Container(
              width: MediaQuery.of(context).size.width,
              color: AppColors.primary,
              child: Stack(
                children: [
                  Stack(
                    children: [
                      SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Image.asset('assets/images/bts_Vector_1.png')),
                      Padding(
                        padding: const EdgeInsets.only(top: 5),
                        child: SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child:
                                Image.asset('assets/images/bts_Vector_2.png')),
                      ),
                    ],
                  ),
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    switchInCurve: Curves.easeInOut,
                    switchOutCurve: Curves.easeInOut,
                    child: subCategories == null
                        ? Column(
                            key: const ValueKey('categories_skeleton'),
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 150),
                              // Using RepaintBoundary for better performance
                              RepaintBoundary(
                                child: Semantics(
                                  label: 'Loading categories',
                                  hint: 'Please wait while categories load',
                                  child: GridView.builder(
                                    shrinkWrap: true,
                                    primary: false,
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            AppDimensions.screenHzPadding),
                                    gridDelegate:
                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 3,
                                      mainAxisSpacing: 10,
                                      crossAxisSpacing: 10,
                                      childAspectRatio: 0.9,
                                    ),
                                    itemCount: 6,
                                    itemBuilder: (ctx, index) {
                                      // Using standardized category skeleton loader
                                      return SkeletonLoaderFactory
                                          .createCategorySkeleton(
                                        radius: 16,
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Column(
                            key: const ValueKey('categories_loaded'),
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(height: 70),
                              Center(
                                  child: CustomText(
                                'Back To School',
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              )),
                              SizedBox(height: 30),
                              GridView.builder(
                                shrinkWrap: true,
                                primary: false,
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppDimensions.screenHzPadding),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 3,
                                  mainAxisSpacing: 16,
                                  crossAxisSpacing: 10,
                                  childAspectRatio: 0.9,
                                ),
                                itemCount: subCategories.length > 6
                                    ? 6
                                    : subCategories.length,
                                itemBuilder: (ctx, index) {
                                  CategoryEntity category =
                                      subCategories[index];

                                  return CategoryCard(
                                    // height: 100,
                                    fontSize: 8,
                                    subCategory: category,
                                    //   category: CategoryEntity(
                                    //       id: '',
                                    //       name: '',
                                    //       collectionId:
                                    //           category.collectionId ?? ''),
                                  );
                                },
                              ),
                            ],
                          ),
                  ),
                ],
              ),
            )
          ],
        );
      },
    );
  }
}
