import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_image.dart';
import '../../../../widgets/custom_text.dart';
import '../../bloc/home bloc/home_bloc.dart';

class MegaMonsoonSection extends StatelessWidget {
  const MegaMonsoonSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Stack(
              alignment: Alignment.topRight,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 45),
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    color: Color(0xFFCBE4F5),
                    image: DecorationImage(
                      image: AssetImage('assets/images/rain.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16, top: 24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          alignment: Alignment.centerRight,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                  right:
                                      MediaQuery.of(context).size.width * 0.104,
                                  bottom: 6),
                              child: Image.asset(
                                'assets/images/Mega_monsoon.png',
                                width: MediaQuery.of(context).size.width * 0.5,
                                // color: Color(0xFF005161),
                              ),
                            ),
                            Image.asset(
                              'assets/images/spark_icon.png',
                              width: MediaQuery.of(context).size.width * 0.5,
                              height: MediaQuery.of(context).size.width * 0.06,
                            ),
                          ],
                        ),
                        RichText(
                          text: TextSpan(
                              text: 'Unlock',
                              children: [
                                TextSpan(
                                    text: 'Great Discounts',
                                    style:
                                        TextStyle(fontWeight: FontWeight.w600)),
                              ],
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFF005161),
                                fontFamily: 'Montserrat',
                              )),
                          textAlign: TextAlign.start,
                        ),
                        SizedBox(height: 180),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: Image.asset(
                    'assets/images/monsoon_character.png',
                    width: MediaQuery.of(context).size.width * 0.35,
                  ),
                ),
              ],
            ),
            Container(
                decoration: BoxDecoration(boxShadow: [
                  BoxShadow(
                    color: Color(0xFFCBE4F5),
                    offset: Offset(20, -20),
                    blurRadius: 10,
                    spreadRadius: 10,
                  ),
                ]),
                child: Image.asset('assets/images/mega_monsoon_bottom.png')),
          ],
        ),
        BlocBuilder<HomeBloc, HomeState>(
          buildWhen: (previous, current) {
            List<CategoryEntity> prevCategories =
                previous.mapOrNull(loaded: (value) => value.categories) ?? [];

            List<CategoryEntity> currCategories =
                current.mapOrNull(loaded: (value) => value.categories) ?? [];

            return prevCategories != currCategories;
          },
          builder: (context, state) {
            List<CategoryEntity?> categories = state.maybeMap(
                loaded: (value) => value.categories ?? [], orElse: () => []);
            return Visibility(
                visible: categories.isNotEmpty,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: SizedBox(
                    height: 120,
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      shrinkWrap: true,
                      primary: false,
                      padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.screenHzPadding),
                      itemCount: categories.length,
                      itemBuilder: (context, index) {
                        CategoryEntity? category = categories[index];
                        return SizedBox(
                            width: MediaQuery.of(context).size.width * 0.22,
                            child: GestureDetector(
                              onTap: () async {
                                HapticFeedback.lightImpact();
                                context.push(RouteNames.products, extra: {
                                  'category': category,
                                });
                                // Log category view event to AppsFlyer
                                await AppsFlyerEvents.categoryView(
                                  category?.id ?? '',
                                  category?.name ?? '',
                                );
                              },
                              child: Container(
                                padding: EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  // color: Color(0xFFFEDA00),
                                  gradient: RadialGradient(colors: [
                                    Color(0xFF117A8F),
                                    Color(0xFF074550),
                                  ], center: Alignment(-0.5, -0.5), radius: 2),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Column(
                                  children: [
                                    ConstrainedBox(
                                      constraints:
                                          BoxConstraints(minHeight: 20),
                                      child: Center(
                                        child: CustomText(
                                          category?.name ?? '',
                                          fontSize: 10,
                                          fontWeight: FontWeight.w500,
                                          maxLines: 2,
                                          textHeight: 1,
                                          textAlign: TextAlign.center,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 5),
                                    Expanded(
                                        child: CustomImage(
                                            imageUrl:
                                                category?.imageUrl ?? '')),
                                  ],
                                ),
                              ),
                            ));
                      },
                      separatorBuilder: (context, index) => SizedBox(width: 12),
                    ),
                  ),
                ));
          },
        ),
      ],
    );
  }
}
