import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/core/themes/color_schemes.dart';

import 'package:rozana/widgets/viewall_category_title.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../data/services/data_loading_manager.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../widgets/lazy_loading_widget.dart';
import '../../bloc/categories_bloc.dart';
import 'category_skeleton_loader.dart';
import 'categoy_card.dart';

class SubCategoriesSection extends StatefulWidget {
  final CategoryEntity? currentSelectedCategory;
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;
  final String? level;

  final bool highliteSelectd;

  const SubCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
    this.level,
    this.currentSelectedCategory,
    this.highliteSelectd = false,
  });

  @override
  State<SubCategoriesSection> createState() => _SubCategoriesSectionState();
}

class _SubCategoriesSectionState extends State<SubCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  @override
  void didUpdateWidget(covariant SubCategoriesSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.parentCategory?.collectionId !=
        oldWidget.parentCategory?.collectionId) {
      _loadCategories();
    }
  }

  Future<void> _loadCategories() async {
    if ((widget.parentCategory == null) ||
        (!CategoriesBloc.loadedSubcategories
            .containsKey(widget.parentCategory?.name))) {
      if (_isLoading) return;

      setState(() {
        _isLoading = true;
      });

      try {
        final categories = widget.parentCategory == null
            ? await _dataManager.loadCategories(pageSize: 20)
            : await _dataManager.loadSubCategories(
                category: widget.parentCategory,
                pageSize: 20,
                level: widget.level);

        setState(() {
          _subCategories.clear();
          _subCategories.addAll(categories);
          _isLoading = false;
          CategoriesBloc
                  .loadedSubcategories[widget.parentCategory?.name ?? ''] =
              categories;
        });
        if (_subCategories.isNotEmpty && widget.onSubcategorySelected != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            widget.onSubcategorySelected!(_subCategories.first);
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } else {
      _subCategories.clear();
      _subCategories.addAll(CategoriesBloc
              .loadedSubcategories[widget.parentCategory?.name ?? ''] ??
          []);

      if (_subCategories.isNotEmpty && widget.onSubcategorySelected != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onSubcategorySelected!(_subCategories.first);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitleBar)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 8, bottom: 8),
            child: ViewAllCategoryTitle(
              title: widget.parentCategory?.name ?? '',
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onSeeAllTap?.call();
              },
              showViewAll: false,
            ),
          ),
        _isLoading
            ? Center(
                child: CategorySkeletonLoader(
                  useGridView: widget.useGridView,
                  showAsRow: widget.showAsRow,
                  gridCrossAxisCount: widget.gridCrossAxisCount,
                  gridChildAspectRatio: widget.gridChildAspectRatio,
                ),
              )
            : widget.showAsRow
                ? _buildCategoryRow()
                : widget.useGridView
                    ? _buildCategoryGrid()
                    : _buildDefaultCategoryGrid(),
      ],
    );
  }

  Widget _buildDefaultCategoryGrid() {
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridCrossAxisCount,
        childAspectRatio: widget.gridChildAspectRatio,
        crossAxisSpacing: 8,
        mainAxisSpacing: 12,
      ),
      itemCount: _subCategories.length,
      itemBuilder: (context, index) {
        CategoryEntity subCategory = _subCategories[index];
        return CategoryCard(
          category: widget.parentCategory,
          subCategory: subCategory,
          radius: 10,
          fontSize: 10,
        );
      },
    );
  }

  Widget _buildCategoryGrid() {
    return GridLazyLoadingWidget<CategoryEntity>(
      items: _subCategories,
      isLoading: _isLoading,
      hasMoreData: false,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridCrossAxisCount,
        childAspectRatio: widget.gridChildAspectRatio,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, category, index) {
        CategoryEntity subCategory = _subCategories[index];

        return CategoryCard(
          category: widget.parentCategory,
          subCategory: subCategory,
        );
      },
    );
  }

  Widget _buildCategoryRow() {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: _subCategories.length,
      itemBuilder: (context, index) {
        CategoryEntity subCategory = _subCategories[index];
        bool isSelected =
            (widget.currentSelectedCategory?.name == subCategory.name) &&
                (widget.highliteSelectd);
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Transform.scale(
            scale: isSelected ? 1 : 0.85,
            child: IntrinsicHeight(
              child: Row(
                children: [
                  VerticalDivider(
                    color: isSelected ? AppColors.primary : Colors.transparent,
                    width: 8,
                    thickness: 8,
                    indent: 0,
                    endIndent: 35,
                    radius: BorderRadius.circular(5),
                  ),
                  Spacer(),
                  CategoryCard(
                    color: Colors.transparent,
                    width: MediaQuery.of(context).size.width * 0.16,
                    imagePadding: EdgeInsets.zero,
                    category: widget.parentCategory,
                    subCategory: subCategory,
                    textPadding: EdgeInsets.only(top: 8),
                    fontSize: 10,
                    onTap: () {
                      widget.onSubcategorySelected?.call(subCategory);
                    },
                  ),
                  Spacer(),
                ],
              ),
            ),
          ),
        );
      },
      separatorBuilder: (context, index) => SizedBox(height: 5),
    );
  }
}
