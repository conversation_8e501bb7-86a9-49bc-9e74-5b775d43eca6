import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/cart_item_model.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../data/mappers/product_mapper.dart';
import '../../../../routes/app_router.dart';
import '../../../cart/bloc/cart_bloc.dart';
import '../../../cart/bloc/cart_event.dart';
import '../../../cart/bloc/cart_state.dart';
import '../../../cart/utils/cart_utils.dart';

class DiscountedProductCard extends StatelessWidget {
  final ProductEntity? product;
  final bool isLoading;
  final double? height;
  final bool staggeredLoading;
  final int staggerIndex;
  final EdgeInsets? imagePadding;
  final EdgeInsets? textPadding;

  const DiscountedProductCard({
    super.key,
    required this.product,
    required this.isLoading,
    this.height,
    this.staggeredLoading = false,
    this.staggerIndex = 0,
    this.imagePadding,
    this.textPadding,
  });

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? ProductCardShimmer()
        : GestureDetector(
            onTap: () async {
              context.push(RouteNames.productDetail, extra: {
                'product': product != null
                    ? ProductMapper.toModel(product!).toJson()
                    : null,
              });
              // Log product view event to AppsFlyer
              await AppsFlyerEvents.productView(
                  sku: product?.skuID,
                  productName: product?.name,
                  price: product?.price,
                  category: product?.category);
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 5, bottom: 5),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20.0),
                          border:
                              Border.all(color: AppColors.neutral200, width: 1),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20.0),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10.0),
                              child: CustomImage(
                                imageUrl: product?.imageUrl,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                height: double.infinity,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Spacer(flex: 4),
                          Flexible(
                            flex: 5,
                            child: QuantityTogglerWidget(
                              height: 38,
                              product: product,
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: textPadding ?? const EdgeInsets.fromLTRB(8, 2, 8, 0),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: 20,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(minHeight: 32),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: CustomText(
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              product?.name ?? '--',
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                              textHeight: 1.4,
                            ),
                          ),
                        ),
                        SizedBox(height: 2),
                        Row(
                          children: [
                            CustomText(
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              '₹${(product?.price ?? 0).toStringAsFixed(0)}',
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              textHeight: 1.4,
                              color: AppColors.neutral700,
                            ),
                            SizedBox(width: 6),
                            IntrinsicWidth(
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  CustomText(
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    '₹${(product?.originalPrice ?? 0).toStringAsFixed(0)}',
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    textHeight: 1.4,
                                    color: AppColors.neutral400,
                                  ),
                                  Transform.rotate(
                                    angle: 2.9,
                                    child: Container(
                                      height: 1,
                                      width: double.infinity,
                                      color: AppColors.neutral400,
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Visibility(
                                visible: false,
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 4),
                                  child: Image.asset(
                                    'assets/new/icons/veg_symbol.png',
                                    width: 16,
                                    height: 16,
                                  ),
                                )),
                            if (product?.variantName != null &&
                                product!.variantName!.isNotEmpty)
                              Flexible(
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 4, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFf7f7f7),
                                  ),
                                  child: CustomText(
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    ' ${product!.variantName}',
                                    fontSize: 10,
                                    color: AppColors.neutral500,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
  }
}

class QuantityTogglerWidget extends StatelessWidget {
  const QuantityTogglerWidget({
    super.key,
    required this.height,
    this.product,
  });
  final double height;
  final ProductEntity? product;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        String productId = product?.id ?? '';
        final quantity = CartUtils.getItemQuantity(
            productId, product?.skuID ?? '', state.cart);
        return Container(
            height: height,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(height / 3),
              gradient: quantity > 0
                  ? RadialGradient(
                      colors: [
                        Color(0xFFBEABD3),
                        Color(0xFF7D56A6),
                      ],
                      center: Alignment(0, -(height * 0.046)),
                      radius: 1,
                      transform: _EllipticalGradientTransform(
                        widthFactor: height * 0.023, // Stretch horizontally
                        heightFactor: height * 0.012, // Compress vertically
                      ),
                    )
                  : null,
              color: quantity > 0 ? null : Colors.white,
              border: quantity > 0
                  ? null
                  : Border.all(
                      color: AppColors.primary500,
                    ),
            ),
            child: quantity > 0
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Minus button
                      Expanded(
                        child: InkWell(
                            splashColor: Colors.transparent,
                            onTap: () async {
                              HapticFeedback.lightImpact();
                              final cartItemId = CartUtils.getCartItemId(
                                  productId, product?.skuID ?? '', state.cart);

                              if (cartItemId != null) {
                                if (quantity > 1) {
                                  context.read<CartBloc>().add(
                                      CartEvent.updateQuantity(
                                          cartItemId,
                                          product?.skuID ?? '',
                                          (quantity - 1).toInt()));
                                } else if (quantity == 1) {
                                  context.read<CartBloc>().add(
                                      CartEvent.removeItem(
                                          cartItemId, product?.skuID ?? ''));
                                }
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 3),
                              child: Image.asset(
                                'assets/new/icons/remove_from_cart.png',
                                width: 12,
                                height: 12,
                              ),
                            )),
                      ),

                      // Quantity display
                      Flexible(
                        child: AbsorbPointer(
                          absorbing: true,
                          child: FittedBox(
                            child: CustomText(
                              '$quantity',
                              color: AppColors.surface,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),

                      // Plus button
                      Expanded(
                        child: InkWell(
                            splashColor: Colors.transparent,
                            onTap: () async {
                              HapticFeedback.lightImpact();
                              final cartItemId = CartUtils.getCartItemId(
                                  productId, product?.skuID ?? '', state.cart);
                              if (cartItemId != null) {
                                String screen =
                                    GoRouterState.of(context).uri.toString();
                                context
                                    .read<CartBloc>()
                                    .add(CartEvent.updateQuantity(
                                      cartItemId,
                                      product?.skuID ?? '',
                                      (quantity + 1).toInt(),
                                      screen: screen,
                                    ));
                              }

                              // widget.onAddToCart?.call();
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 3),
                              child: Image.asset(
                                'assets/new/icons/add_to_cart.png',
                                width: 12,
                                height: 12,
                              ),
                            )),
                      ),
                    ],
                  )
                : InkWell(
                    onTap: () {
                      String screen = GoRouterState.of(context).uri.toString();
                      HapticFeedback.lightImpact();
                      context.read<CartBloc>().add(CartEvent.addItem(
                          item: CartItemModel(
                            productId: productId,
                            name: product?.name,
                            price: product?.originalPrice,
                            imageUrl: product?.imageUrl,
                            quantity: 1,
                            facilityId: product?.facilityId,
                            facilityName: product?.facilityName,
                            unit: 'item',
                            discountedPrice: product?.price,
                            skuID: product?.skuID,
                            availableQuantity: product?.availableQty,
                            maxQuantity: product?.maxLimit,
                          ),
                          screen: screen));
                    },
                    child: Center(
                      child: const CustomText(
                        'ADD',
                        color: AppColors.primary500,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ));
      },
    );
  }
}

// Custom GradientTransform to apply elliptical scaling
class _EllipticalGradientTransform extends GradientTransform {
  final double widthFactor;
  final double heightFactor;

  const _EllipticalGradientTransform({
    required this.widthFactor,
    required this.heightFactor,
  });

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    // Calculate the center of the bounds
    final double centerX = bounds.center.dx;
    final double centerY = bounds.center.dy;

    // Create a translation matrix to move the center to the origin (0,0)
    final Matrix4 matrix = Matrix4.identity()
      ..translate(centerX, centerY)
      ..scale(widthFactor, heightFactor) // Apply the non-uniform scale
      ..translate(-centerX, -centerY); // Move back to original center

    return matrix;
  }
}

class ProductCardShimmer extends StatelessWidget {
  const ProductCardShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
            child: CustomShimmer(
          radius: 20,
          baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
          highlightColor: Color(0xFFD8D5EA),
        )),
        SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.fromLTRB(8, 4, 8, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 10),
                child: CustomShimmer(
                  height: 16,
                  width: double.infinity,
                  baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                  highlightColor: Color(0xFFD8D5EA),
                ),
              ),
              SizedBox(height: 8),
              CustomShimmer(
                height: 16,
                width: 100,
                baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                highlightColor: Color(0xFFD8D5EA),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  CustomShimmer(
                    height: 16,
                    width: 16,
                    baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                    highlightColor: Color(0xFFD8D5EA),
                  ),
                  SizedBox(width: 4),
                  CustomShimmer(
                    height: 16,
                    width: 90,
                    baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                    highlightColor: Color(0xFFD8D5EA),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
