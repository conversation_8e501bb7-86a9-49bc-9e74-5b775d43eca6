import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

enum PaymentAnimationState {
  processing,
  success,
}

class PaymentConfirmationScreen extends StatefulWidget {
  final PaymentAnimationState initialState;
  final VoidCallback? onAnimationComplete;

  const PaymentConfirmationScreen({
    super.key,
    this.initialState = PaymentAnimationState.processing,
    this.onAnimationComplete,
  });

  @override
  State<PaymentConfirmationScreen> createState() =>
      _PaymentConfirmationScreenState();
}

class _PaymentConfirmationScreenState extends State<PaymentConfirmationScreen>
    with SingleTickerProviderStateMixin {
  late PaymentAnimationState _currentState;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _currentState = widget.initialState;
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // If starting with processing animation, automatically transition to success after completion
    if (_currentState == PaymentAnimationState.processing) {
      _controller.addStatusListener((status) {
        if (status == AnimationStatus.completed &&
            _currentState == PaymentAnimationState.processing) {
          // Transition to success animation after processing completes
          setState(() {
            _currentState = PaymentAnimationState.success;
            _controller.reset();
            // Use a longer duration for the success animation
            _controller.duration = const Duration(seconds: 3);
            _controller.forward();
          });
        } else if (status == AnimationStatus.completed &&
            _currentState == PaymentAnimationState.success) {
          // Delay before calling onAnimationComplete to ensure animation is visible
          Future.delayed(const Duration(milliseconds: 1500), () {
            if (widget.onAnimationComplete != null) {
              widget.onAnimationComplete!();
            }
          });
        }
      });
    }

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Center(
                  child: _currentState == PaymentAnimationState.processing
                      ? _buildProcessingAnimation()
                      : _buildSuccessAnimation(),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 24.0, vertical: 32.0),
                child: Text(
                  _currentState == PaymentAnimationState.processing
                      ? 'Confirming your payment...'
                      : 'Payment successful!',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 24.0, vertical: 16.0),
                child: Text(
                  _currentState == PaymentAnimationState.processing
                      ? 'Please wait while we process your payment'
                      : 'Your order has been placed successfully',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 48),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProcessingAnimation() {
    return Lottie.asset(
      'lotties/order_process.json',
      controller: _controller,
      width: MediaQuery.of(context).size.width * 0.7,
      height: MediaQuery.of(context).size.width * 0.7,
      fit: BoxFit.contain,
      animate: true,
      onLoaded: (composition) {
        _controller.duration = composition.duration;
        _controller.forward();
      },
    );
  }

  Widget _buildSuccessAnimation() {
    return Lottie.asset(
      'lotties/success.json',
      controller: _controller,
      width: MediaQuery.of(context).size.width * 0.7,
      height: MediaQuery.of(context).size.width * 0.7,
      fit: BoxFit.contain,
      animate: true,
      onLoaded: (composition) {
        _controller.duration = composition.duration;
        _controller.forward();
      },
    );
  }
}
