import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_svg/flutter_svg.dart';

// Helper function to handle CORS for web
String resolveImageUrl(String url) {
  if (kIsWeb) {
    return "https://corsproxy.io/?${Uri.encodeFull(url)}";
  }
  return url;
}

class CustomImage extends StatelessWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final String imageType;
  final Color? imageColor;

  const CustomImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.imageType = 'product',
    this.imageColor,
  });

  String get fallbackImagePath {
    return imageType == 'product'
        ? 'assets/products/vegetables.png'
        : 'assets/categories/shopping-bag.png';
  }

  bool get isNetworkImage {
    return imageUrl != null &&
        (imageUrl!.startsWith('http://') || imageUrl!.startsWith('https://'));
  }

  bool get isSvgImage {
    return imageUrl != null && (imageUrl!.toLowerCase().contains('.svg'));
  }
  @override
  Widget build(BuildContext context) {
    final imageWidget = isSvgImage
        ? isNetworkImage
            ? SvgPicture.network(
                resolveImageUrl(imageUrl!),
                fit: fit,
                width: width,
                height: height,
                errorBuilder: (context, error, stackTrace) =>
                    _buildFallbackImage(),
                colorFilter: imageColor != null
                    ? ColorFilter.mode(imageColor!, BlendMode.srcIn)
                    : null,
              )
            : SvgPicture.asset(
                imageUrl!,
                fit: fit,
                width: width,
                height: height,
                errorBuilder: (context, error, stackTrace) =>
                    _buildFallbackImage(),
              )
        : isNetworkImage
            ? CachedNetworkImage(
                imageUrl: resolveImageUrl(imageUrl!),
                fit: fit,
                width: width,
                height: height,
                errorWidget: (context, url, error) => _buildFallbackImage(),
              )
            : Image.asset(
                imageUrl ?? fallbackImagePath,
                fit: fit,
                width: width,
                height: height,
                errorBuilder: (context, error, stackTrace) =>
                    _buildFallbackImage(),
                color: imageColor,
              );

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: imageWidget,
    );
  }

  Widget _buildFallbackImage() {
    return Image.asset(
      fallbackImagePath,
      width: width,
      height: height,
      fit: fit,
    );
  }
}
