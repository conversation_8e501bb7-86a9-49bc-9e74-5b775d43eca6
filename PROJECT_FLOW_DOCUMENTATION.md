# Rozana E-commerce App - Project Flow Documentation

## 🏗️ Application Architecture Overview

The Rozana e-commerce app is built using **Flutter** with **Clean Architecture** principles, **BLoC state management**, and **multi-platform support** (mobile + web view).

## 🚀 Application Entry Point & Initialization Flow

### 1. Main Entry Point (`lib/main.dart`)
```
main() → WidgetsFlutterBinding.ensureInitialized() → setupDI() → AppConfig.init() → runApp(MyApp)
```

**Key Steps:**
- **Dependency Injection Setup**: Initializes GetIt container with all services and BLoCs
- **App Configuration**: Loads environment-specific configurations
- **Root BLoC Provider**: Provides AppBloc at the root level
- **App Started Event**: Triggers initial app state determination

### 2. App Widget Structure (`lib/App/app.dart`)
```
MyApp → LayoutBuilder → WebViewBloc → BlocBuilder → [WebScreen | CoreAppWidget]
```

**Responsive Design Logic:**
- **Screen Width > 600px**: Shows WebScreen (desktop/tablet view)
- **Screen Width ≤ 600px**: Shows CoreAppWidget (mobile view)
- **Dynamic Switching**: Automatically switches between views based on screen size

## 🔄 State Management Architecture

### BLoC Pattern Implementation
- **Global BLoCs**: AppBloc, ThemeBloc, LanguageBloc, LocationBloc, CartBloc, ConnectivityBloc
- **Feature-Specific BLoCs**: LoginBloc, HomeBloc, ProductListingBloc, SearchBloc, etc.
- **Memory Management**: Proper BLoC lifecycle with cleanup services

### Key Global States
1. **AppBloc**: Authentication state, app initialization
2. **LocationBloc**: User location, address management
3. **CartBloc**: Shopping cart state, buy now functionality
4. **WebViewBloc**: View mode switching (web/mobile)

## 🗺️ Navigation & Routing Flow

### Router Configuration (`lib/routes/app_router.dart`)
```
GoRouter → Splash Screen → [Authentication Check] → [Home | Login]
```

**Route Protection Logic:**
- **Authenticated Users**: Redirect from login/splash to home
- **Unauthenticated Users**: Redirect protected routes to login
- **Deep Link Handling**: Firebase Auth deep links support

### Main Route Structure
```
/splash → /login → /home (with bottom navigation)
├── /categories
├── /cart
├── /wishlist
└── /profile

Standalone Routes:
├── /products
├── /product-detail
├── /search
├── /checkout
├── /orders
└── /address-*
```

## 🏪 Core Business Flows

### 1. User Authentication Flow
```
Splash → Check Auth State → [Login Screen | Home Screen]
Login → OTP Verification → Home Screen
```

### 2. Location & Store Selection Flow
```
App Start → Location Permission → Get Coordinates → TypesenseService.initializeWithCoordinates()
→ Find Store by Location → Set Current Store → Enable Product Search
```

**TypesenseService Integration:**
- **Store Detection**: Uses polygon coordinates to find serviceable stores
- **Location-Based Products**: Filters products by current store
- **Serviceability Check**: Determines if location is serviceable

### 3. Product Discovery Flow
```
Home Screen → [Categories | Search | Featured Products]
Categories → Subcategories → Product Listing → Product Details
Search → TypesenseService.globalSearch() → Search Results → Product Details
```

### 4. Shopping Cart Flow
```
Product Details → Add to Cart → CartBloc.addItem()
Cart Screen → Modify Quantities → Checkout → Payment (Razorpay)
Buy Now → Temporary Cart → Direct Checkout → Clear Buy Now Items
```

### 5. Order Management Flow
```
Checkout → Payment Success → Order Creation → Order History
Order Details → Track Order → Reorder Functionality
```

## 🔍 Search & Discovery System

### Typesense Integration (`lib/features/search/services/typesense_service.dart`)
```
TypesenseService (Singleton)
├── Store Detection (Polygon-based)
├── Product Search (Multi-collection)
├── Category Search
├── Global Search
└── Search Suggestions
```

**Search Collections:**
- **Products Collection**: Main product catalog
- **Categories Collection**: Category hierarchy
- **Stores Collection**: Store locations and polygons
- **Top Products Collection**: Featured/trending products
- **Sliders Collection**: Banners and promotional content

**Search Features:**
- **Typo Tolerance**: Handles spelling mistakes
- **Auto-suggestions**: Real-time search suggestions
- **Faceted Search**: Filter by category, brand, price
- **Location-based**: Results filtered by serviceable store

## 🌐 Multi-Platform Architecture

### Web View Implementation
```
Screen Width Check → WebViewBloc → [WebScreen | CoreAppWidget]
WebScreen → Company Info + Mobile Frame → CoreAppWidget (reused)
```

**Key Features:**
- **Responsive Design**: Automatic view switching
- **Code Reuse**: CoreAppWidget used in both mobile and web frame
- **Mobile Frame**: Google Pixel 9 Pro dimensions for web view
- **Navigation Handling**: Auto-hide navigation in mobile frame

## 🛠️ Core Services & Integrations

### 1. Environment Configuration
```
Environment Variables → AppConfig → Service Configuration
├── API_BASE_URL
├── TYPESENSE_API_KEY
├── FIREBASE_CONFIG
└── AMPLITUDE_API_KEY
```

### 2. Network Layer
```
Dio Client → Interceptors → API Endpoints → Response Processing
├── Authentication Headers
├── Request/Response Logging
├── Error Handling
└── Retry Logic
```

### 3. Local Storage
```
SharedPreferences → AppPreferencesService
├── User Session Data
├── Cart Persistence
├── Address Storage
└── App Settings
```

### 4. Payment Integration
```
Razorpay Service → Platform-specific Implementation
├── Mobile: razorpay_flutter
├── Web: razorpay_web.js
└── Payment Success/Failure Handling
```

## 🌍 Localization & Theming

### Multi-language Support
```
LanguageBloc → [English | Hindi] → ARB Files → App Localizations
```

### Theme Management
```
ThemeBloc → [Light | Dark] → AppTheme → Material Design
```

## 📱 Feature Modules

### 1. Authentication (`lib/features/auth/`)
- Login/Signup with OTP
- Firebase Authentication
- Session Management

### 2. Home (`lib/features/home/<USER>
- Dashboard with categories
- Featured products
- Banners and promotions

### 3. Products (`lib/features/products/`)
- Product listing with filters
- Product details with variants
- Image gallery and reviews

### 4. Cart (`lib/features/cart/`)
- Add/remove items
- Quantity management
- Buy now functionality
- Checkout process

### 5. Search (`lib/features/search/`)
- Global search across products
- Search suggestions
- Category-based search

### 6. Location (`lib/features/location/`)
- Address management
- Location detection
- Map integration

### 7. Orders (`lib/features/order/`)
- Order history
- Order tracking
- Reorder functionality

### 8. Profile (`lib/features/profile/`)
- User profile management
- Settings and preferences

## 🔒 Security & Performance

### Security Features
- Environment-based configuration
- No hardcoded API keys
- Secure authentication flow
- Code obfuscation in production

### Performance Optimizations
- BLoC memory management
- Image caching and optimization
- Lazy loading of data
- Efficient state management

## 🧪 Testing & Quality Assurance

### Testing Structure
- Widget tests for UI components
- BLoC tests for state management
- Integration tests for user flows
- Memory management tests

### Code Quality
- Flutter lints for code standards
- Freezed for immutable classes
- Clean architecture principles
- Dependency injection for testability

## 🔄 Detailed Flow Diagrams

### Application Startup Flow
```mermaid
graph TD
    A[App Launch] --> B[WidgetsFlutterBinding.ensureInitialized]
    B --> C[setupDI - Dependency Injection]
    C --> D[AppConfig.init - Environment Setup]
    D --> E[BlocProvider - AppBloc]
    E --> F[AppStarted Event]
    F --> G{Screen Width Check}
    G -->|>600px| H[WebScreen]
    G -->|≤600px| I[CoreAppWidget]
    H --> J[Mobile Frame + Company Info]
    I --> K[Material App Router]
    J --> K
    K --> L[GoRouter Navigation]
    L --> M{Authentication Check}
    M -->|Authenticated| N[Home Screen]
    M -->|Not Authenticated| O[Login Screen]
```

### User Authentication Flow
```mermaid
graph TD
    A[User Opens App] --> B{Check Auth State}
    B -->|Authenticated| C[Navigate to Home]
    B -->|Not Authenticated| D[Show Login Screen]
    D --> E[User Enters Phone Number]
    E --> F[Send OTP Request]
    F --> G[OTP Verification Screen]
    G --> H{OTP Valid?}
    H -->|Yes| I[Create/Update User Session]
    H -->|No| J[Show Error Message]
    I --> K[Navigate to Home]
    J --> G
```

### Product Search & Discovery Flow
```mermaid
graph TD
    A[User Search Input] --> B[TypesenseService.globalSearch]
    B --> C{Store ID Available?}
    C -->|No| D[Wait for Store Initialization]
    C -->|Yes| E[Build Search Parameters]
    D --> E
    E --> F[Query Typesense Collections]
    F --> G[Process Search Results]
    G --> H[Return Formatted Products]
    H --> I[Update Search UI]

    J[Location Change] --> K[TypesenseService.reinitializeWithCoordinates]
    K --> L[Find Store by Polygon]
    L --> M{Store Found?}
    M -->|Yes| N[Set Current Store ID]
    M -->|No| O[Mark Location Not Serviceable]
    N --> P[Enable Product Search]
    O --> Q[Show Service Unavailable]
```

### Shopping Cart & Checkout Flow
```mermaid
graph TD
    A[Add to Cart] --> B[CartBloc.addItem]
    B --> C[Update Cart State]
    C --> D[Persist to Local Storage]
    D --> E[Update UI]

    F[Buy Now] --> G[CartBloc.addBuyNowItem]
    G --> H[Create Temporary Cart]
    H --> I[Navigate to Checkout]
    I --> J[Payment Process]
    J --> K{Payment Success?}
    K -->|Yes| L[Clear Buy Now Items]
    K -->|No| M[Return to Cart]
    L --> N[Create Order]
    M --> I
```

## 🛠️ Technical Implementation Details

### Dependency Injection Setup
```dart
// lib/core/dependency_injection/di_setup.dart
void setupDI() {
  // Core Services
  getIt.registerLazySingleton<ApiClient>(() => ApiClient());
  getIt.registerLazySingleton<TypesenseService>(() => TypesenseService());

  // BLoCs (Singletons for global state)
  getIt.registerLazySingleton<AppBloc>(() => AppBloc());
  getIt.registerLazySingleton<CartBloc>(() => CartBloc());
  getIt.registerLazySingleton<LocationBloc>(() => LocationBloc());

  // Feature-specific BLoCs (Factory for multiple instances)
  getIt.registerFactory<LoginBloc>(() => LoginBloc());
  getIt.registerFactory<ProductListingBloc>(() => ProductListingBloc());
}
```

### Environment Configuration
```dart
// lib/core/config/environment_config.dart
class EnvironmentConfig {
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://oms-dev.rozana.tech/',
  );

  static const String typesenseApiKey = String.fromEnvironment(
    'TYPESENSE_API_KEY',
    defaultValue: '',
  );

  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
}
```

### TypesenseService Key Methods
```dart
// Store initialization with coordinates
Future<void> initializeWithCoordinates(double lat, double lng) async {
  final store = await findStoreByCoordinates(lat, lng);
  if (store != null) {
    _currentStoreId = store['id']?.toString();
    _isLocationServiceable = true;
  } else {
    _isLocationServiceable = false;
  }
  _storeIdInitialized.complete();
}

// Global search across products
Future<Map<String, List<Map<String, dynamic>>>> globalSearch({
  required String query,
}) async {
  await storeIdInitialized; // Wait for store initialization

  final searchParameters = {
    'q': query,
    'query_by': 'name,display_alias,category_name',
    'filter_by': 'store_id:$_currentStoreId && is_available:=true',
    'group_by': 'sku',
    'group_limit': '1',
  };

  final searchResult = await _client
      .collection(TypesenseConfig.productsCollection)
      .documents
      .search(searchParameters);

  return {'products': _processProductResults(searchResult)};
}
```

## 📊 Data Flow Architecture

### State Management Pattern
```
User Action → Event → BLoC → State → UI Update
     ↓
Local Storage ← Repository ← API Service
```

### Cart State Management
```dart
// Cart Events
sealed class CartEvent {
  const CartEvent();

  const factory CartEvent.init() = _Init;
  const factory CartEvent.addItem(ProductModel product) = _AddItem;
  const factory CartEvent.removeItem(String productId) = _RemoveItem;
  const factory CartEvent.addBuyNowItem(ProductModel product) = _AddBuyNowItem;
  const factory CartEvent.clearBuyNowItems() = _ClearBuyNowItems;
}

// Cart States
sealed class CartState {
  const CartState();

  const factory CartState.initial() = _Initial;
  const factory CartState.loading() = _Loading;
  const factory CartState.loaded(List<CartItemModel> items) = _Loaded;
  const factory CartState.error(String message) = _Error;
}
```

### Location-Based Service Discovery
```dart
// Location detection and store mapping
class LocationBloc extends Bloc<LocationEvent, LocationState> {
  LocationBloc() : super(const LocationState.initial()) {
    on<LocationEvent>((event, emit) async {
      await event.when(
        started: () async {
          emit(const LocationState.loading());

          // Get current location
          final position = await _getCurrentPosition();

          // Initialize Typesense with coordinates
          await TypesenseService().initializeWithCoordinates(
            position.latitude,
            position.longitude,
          );

          emit(LocationState.loaded(position));
        },
        addressChanged: (address) async {
          // Reinitialize store when address changes
          await TypesenseService().reinitializeWithCoordinates(
            address.latitude,
            address.longitude,
          );
        },
      );
    });
  }
}
```

This documentation provides a comprehensive overview of the Rozana e-commerce app's architecture, flows, and technical implementation details.
